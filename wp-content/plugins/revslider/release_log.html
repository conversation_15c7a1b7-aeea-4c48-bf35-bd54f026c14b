<div class="slider-revolution-update-list">
<h3 class="version-number">Version 5.4.6 StarPath (15th August 2017)</h3>
<div class="newfeature"><strong style="font-weight:700">NEW FEATURES</strong></div>
<ul>
	<li>Added autoplay functionality for HTML5 videos on mobile</li>
	<li>Added loadbalancing functionality for further stability of premium features</li>
</ul>
<div class="bugfix"><strong style="font-weight:700">BUGFIXES</strong></div>
<ul>
	<li>CSS Issue in Backend where RevSlider Toolbar would overlap the WP Login/Logout popup in some cases</li>
</ul>

<h3 class="version-number">Version 5.4.5.2 StarPath (5th August 2017)</h3>
<div class="newfeature"><strong style="font-weight:700">NEW FEATURES</strong></div>
<ul>
	<li>Added compatibility for Weather and 360 Degree AddOn</li>
</ul>

<h3 class="version-number">Version 5.4.5.1 StarPath (10th June 2017)</h3>
<div class="bugfix"><strong style="font-weight:700">BUGFIXES</strong></div>
<ul>
	<li>Fixed Color Picker PHP bug caused in old PHP versions</li>
</ul>

<h3 class="version-number">Version 5.4.5 StarPath (23rd May 2017)</h3>
<div class="newfeature"><strong style="font-weight:700">NEW FEATURES</strong></div>
<ul>
	<li>Set Parallax BG and Layers Speed/Delay Globally independent. 15 Levels Depth still available ! </li>
	<li>Added Color Animation Attributes like Background and Font Color for Shape and Text Layers</li>
	<li>Added Brightness Filter Attribute for Idle, Hover, Start and End Animation frames to provide new Effects on Layers</li>
	<li>Added Grayscale, Brightness, Blur Slide Transitions in different Combinations</li>	
</ul>
<div class="change"><strong style="font-weight:700">CHANGES</strong></div>
<ul>
	<li>Updated Google Fonts list</li>
</ul>

<div class="bugfix"><strong style="font-weight:700">BUGFIXES</strong></div>
<ul>
	<li>Fixed HTML Export gradiants not working properly</li>
	<li>Fixed YouTube and Vimeo Video Rewind issue on reenter specific slides.</li>
	<Li>Fixed Toggle FullScreen Mode where YouTube and Vimeo Videos were not Play any more</Li>
	<li>Ken Burn Slide Effect Issues at first Slide Change has been fixed</li>
	<li>First Covered BG Video in some Templates and Themes were caclualted on the wrong Height Base. Issue is fixed due a double call on setSize at preparing the Video</li>
	<li>Safari Scroll, 3D and Blur Effects with missing layers Bug has been fixed by adding  a preserve-3d transform style on Parallax Wrap level to the current layers</li>
	<li>Image Filters on Ken Burn Animated BG Elements are working now fine.  FireFox is still a bit Laggy if CSS Filters are enabled on Ken Burn Elements</li>
	<li>Fixed Responsive Levels not working properly in certain constellations</li>
	<li>Fixed Background Default Volume Settings in Backend</li>
	<li>Fixed Rotation issues on Slide Main Image transitions (like Box Slot Animation Rotated)</li>
	<Li>Updaed Colorpicker JS to fix some issues with Progress Bar.</Li>
	<li>Fixed HTML5 volume not beeing written</li>
</ul>

<h3 class="version-number">Version 5.4.3.2 StarPath (9th May 2017)</h3>
<div class="newfeature"><strong style="font-weight:700">NEW FEATURES</strong></div>
<ul>
	<li>Added action revslider_preview_slider_footer to be used by new AddOn Before/After</li>
</ul>
<div class="bugfix"><strong style="font-weight:700">BUGFIXES</strong></div>
<ul>
	<li>Changed Google Fonts | to %7C for HTML/CSS Validation</li>
	<li>Fixed several typos</li>
</ul>

<h3 class="version-number">Version ******* StarPath (25th April 2017)</h3>
<div class="change"><strong style="font-weight:700">CHANGES</strong></div>
<ul>
	<li>Typo in API descriptions removed</li>
</ul>
<div class="bugfix"><strong style="font-weight:700">BUGFIXES</strong></div>
<ul>
	<li>Fixed Slider/Slide Preview not working properly in 5.4.3</li>
	<li>Fixed Export to HTML missing some JavaScript in 5.4.3</li>
	<li>Added further WooCommerce 3.0+ compatibility with Out of Stock and Featured management</li>
</ul>

<h3 class="version-number">Version 5.4.3 StarPath (21th April 2017)</h3>
<div class="newfeature"><strong style="font-weight:700">NEW FEATURES</strong></div>
<ul>
	<li>Introducing jQuery.fn.getRSVersion() and jQuery.fn.getRSVersion(true) functions to get Core and all Loded Module Version numbers.</li>
</ul>
<div class="bugfix"><strong style="font-weight:700">BUGFIXES</strong></div>
<ul>
	<li>Added WooCommerce compatibility for 3.0+</li>
	<li>Fixed Several Typos</li>
	<li>Fixed Instagram bug, where Slides would not show if maximum entries are higher than what was fetched</li>
	<li>Fixed Deep Linking in Carousel Slider</li>
	<li>Fixed Issue where YouTube Video Background in Pause / End Mode shows the Default YouTube Cover image instead of the Predefined poster Image</li>
	<li>Fixed Wrong Navigation Direction / Thumbnail Images on Arrow Navigation in some kind of Cases</li>
	<li>Fixed Disable on Mobile not working if Slider was added through a Widget</li>
</ul>

<h3 class="version-number">Version 5.4.2 StarPath (08th April 2017)</h3>
<div class="newfeature"><strong style="font-weight:700">NEW FEATURES</strong></div>
<ul>
	<li>InLine Play of HTML5 Videos on Android and on iOS are supported</li>
	<li>New Content Source "Current Post/Page"</li>
	<li>New Author Information Metas for Post Sliders</li>
</ul>
<div class="bugfix"><strong style="font-weight:700">BUGFIXES</strong></div>
<ul>
	<li>Fixed FadeFromRight Animation</li>
	<li>Fixed RevKill() Bugs and updated Focus/Blur features which will only triggered once. Blur Focus Listener will also called once per DOM instead of per Slider</li>
	<li>Fixed Columns Margin calculation. Columns now can be perfectly spaced with the 4 level margin and padding attributes</li>	
	<li>Fixed Column BG Animation and Position Calculations.</li>
	<li>Fixed Facebook API Issue</li>
</ul>

<h3 class="version-number">Version 5.4.1 StarPath (28th February 2017)</h3>
<div class="bugfix"><strong style="font-weight:700">BUGFIXES</strong></div>
<ul>
	<li>Fixed Colorpicker Issues where wrong old Color values broke the Convert process and negative influenced the Slide Editor</li>	
	<li>Fixed Scroll Below bugs</li>	
	<li>Fixed Notices of undefined variables that can occur after updating</li>	
</ul>

<h3 class="version-number">Version 5.4 StarPath (22nd February 2017)</h3>
<div class="newfeature"><strong style="font-weight:700">NEW FEATURES</strong></div>
<ul>
	<li>Added Blur Effect on Ken Burn Animation. Start and End Value can be defined</li>
	<li>Added Letter Spacing on Multiple Levels</li>	
	<li>Introducing new Layer animations: Block Animation (left,right,top,bottom)</li>
	<li>Introducing a new Color Picker which allows to pick Alpha Values and Gradient Colors as well</li>
	<li>Communication with ThemePunch servers now only via SSL</li>
	<li>Added a feature to allow autoplay HTML5 Videos on Android Devices also</li>
	<li>Added SEO follow/nofollow option to simple link actions</li>
	<li>Added Split Animation Direction like forwards, backwards, middle to edge, edge to middle, random</li>
	<li>Random Split Transitions will calculate on demand Random values per Splitted element</li>
	<li>Added Split Animation Cycles to create Animation patterns on Splitted elements</li>
	<li>Introducing new AddOn "Slicey"</li>
	<li>Introducing new AddOn "FilmStrip"</li>
	<li>Introducing new AddOn "Adjacent Posts"</li>
	<li>Introducing new AddOn "Login Page Slider"</li>
	<li>Introducing new AddOn "404 Page"</li>
	<li>Introducing new AddOn "Post Featured Slider"</li>
</ul>
<div class="change"><strong style="font-weight:700">CHANGES</strong></div>
<ul>
	<li>Added a new Option to enable/disable Desktop Swipe functions. This will allow you to enable Swipe on Mobile Devices, and still make Content Selectable on Desktop Systems</li>	
</ul>
<div class="bugfix"><strong style="font-weight:700">BUGFIXES</strong></div>
<ul>
	<li>Fixed an Issue where Template Slides could not be added due the Slide Editor due some License Code issues</li>	
	<li>Fixed some GUI Issues</li>
	<li>Slider Embed Option was not available on New WP Pages. TinyMCE Issues has been solved</li>
	<li>Fixed Bug with Slider Alias will not save</li>
	<li>Issues with background image url in export files has been fixed</li>
	<li>Extended Max. Slide Time to Unlimited</li>
	<li>Fixed Slide Timeline Bar Visual Effect on  Slide Length change event</li>
	<li>Bug fixed where "Split Out Animation" breaks none "Split In Animation" on Layers</li>
	<li>Fixed bugs with SVG Stroke Animation / Coloring on Simple Idle, and on Idle/Hover status</li> 
	<li>Group Embeded Layer Timing issues has been fixed. Out / In Animation Time points were  wrong calculated in some kind of cases</li>
</ul>

<h3 class="version-number">Version 5.3.1.6 StarPath (5th January 2017)</h3>
<div class="newfeature"><strong style="font-weight:700">NEW FEATURES</strong></div>
<ul>
	<li>Introducing placeholders to gallery sliders (already available for post/stream sources)</li>
	<li>Added Ease and Speed to Scroll Below Action to make individual Scroll Effects</li>
</ul>
<div class="bugfix"><strong style="font-weight:700">BUGFIXES</strong></div>
<ul>
	<li>Slide Link was not linking to the correct Slides in some cases.</li>	
	<li>Slide Link was not visible at start</li>
	<li>Fixed Bug in "Add New" Page/Post, Slider Selector overview in Visual Composer not working properly</li>
	<li>Fixed no "Add RevSlider" icon in WP Editor for new posts/pages</li>
	<li>Fixed CSS Navigation bug where under certain circumstances CSS ID's were wrongy added</li>
	<li>Fixed bug where custom navigation settings might not get correctly stripslashed()</li>	
	<li>Fixed for $add_static is not defined</li>	
</ul>

<h3 class="version-number">Version ******* StarPath (10th December 2016)</h3>
<div class="newfeature"><strong style="font-weight:700">NEW FEATURES</strong></div>
<ul>
	<li>Added option to delete User Slide Templates</li>
</ul>
<div class="change"><strong style="font-weight:700">CHANGES</strong></div>
<ul>
	<li>Layout of Sticky Layers Settings has been changed</li>
	<li>Removed unneeded CSS Lines and minified the CSS Export of settings.css</li>
</ul>
<div class="bugfix"><strong style="font-weight:700">BUGFIXES</strong></div>
<ul>
	<li>Fixed an issue where static element Position Calculation was broken in some kind of cases</li>
	<li>Fixed an issue where inline styled Content lost its FontWeight, Line Height and Font Size in some kind of cases</li>	
	<li>Fixed an incomatability with our Blank Fullwidth Template and WordPress 4.7</li>	
	<li>Fixed a Rendering issue with Row Oversizing in case Image element added into Columns</li>
	<li>Fixed "Child Elements Responsive" to stay at "off" after Slide Editor reload</li>
	<li>ID of Wrapper Elements added via Option was not available on Frontend</li>
	<li>Fixed date selector for Visible From/To z-index issue</li>
	<li>Fixed Layer Action issues where multiple Actions on same Layer was not recognised on FrontEnd</li>
	<li>Fixed Border issues on Buttons</li>
	<li>Loading of Video Elements in Rows driven by Actions was not Showing up in some kind of rare cases</li>
</ul>


<h3 class="version-number">Version ******* StarPath (8th December 2016)</h3>
<div class="change"><strong style="font-weight:700">CHANGES</strong></div>
<ul>
	<li>Merged Google Font loading into less calls</li>
	<li>Removed unneeded CSS Lines and minified the CSS Export of settings.css</li>
</ul>
<div class="bugfix"><strong style="font-weight:700">BUGFIXES</strong></div>
<ul>
	<li>Hidden "Max Width" field is available in Auto Mode again</li>
	<li>Fixed notice in output.class.php for certain Sliders</li>
	<li>Fixed a strange Effect on Scroll where Stick Style Menu was not clearly docked on the top, but was flickering before docking</li>
</ul>


<h3 class="version-number">Version 5.3.1 StarPath (1st December 2016)</h3>
<div class="newfeature"><strong style="font-weight:700">NEW FEATURES</strong></div>
<ul>
	<li>Added option to show layers on all Slides in Carousel mode</li>
	<li>Added option to use YouTube video cover for YouTube video background on slides</li>
	<li>Added box-shadow:none css default settings on a tags within the Layers to avoid Wordpress Basic Theme borders</li>
	<li>New Option added to fade Out Hero Content optional when Parallax Scroll activated, like Slider BG, Layer, Static Layer, Parallax Layer</li>
	<li>Added an option to set the Group Sizes by %. Content of Groups can be set also % based</li>
	<li>Added new Option for Easing and Speed of Carousel Slides</li>
	<li>Added an option which allows to show Carousel first in Viewport.</li>
	<li>Added option for layers to add an tabindex.</li>
</ul>
<div class="change"><strong style="font-weight:700">CHANGES</strong></div>
<ul>
	<li>Added Version Checking for Loaded Modules and some function to handle Cache issues</li>
	<li>Redesigned the Import Process of Slider and Slide Templates</li>
	<li>Carousel Slides will always fade in first without a Start Slide effect which was annoying in some kind of situations</li>
	<li>Carousel Slider will act on Swipe even due Clickable elements</li>
	<li>Extended Idle/Hover Style Editor in Slide Editor mode for quicker work processes</li>
	<li>jQuery and Slider CSS Editor also available due Slide Editor mode to simplfy the Slide building processes</li>
	<li>Static/Global Styles are now obsolete</li>
	<li>Font Awesome Library Updated to 4.7.0</li>
</ul>
<div class="bugfix"><strong style="font-weight:700">BUGFIXES</strong></div>
<ul>
	<li>Fixed Image Filter issues related to Slide Changes</li>
	<li>Fixed Slide Transitions where Transion should happen based on the Slide Direction</li>
	<li>Flickering issue by Static Layers with Action fixed</li>
	<li>Fixed an issue where Quick and Long LAyer Actions made Layers unavailable</li>
	<li>Fixed Order issues on Row Duplication</li>	
	<li>RTL Backend Issues with the new Objet Library has been fixed</li>
	<li>Fixed missing Styles after Hovering Layers in Firefox</li>
	<li>Toggled Content without Action was not swapping its Toggled Status. Issue is fixed now</li>
	<li>Fixed Grid Based Static Layer Positions in Carousel Sliders</li>
	<li>Fixed Issue where adding New Layer due Context menu was broken</li>
	<li>Fixed Backend Margin calculation issue on Backend. Frontend and Backend was behaving differently. Now Frontend view covers backend view</li>
	<li>Fixed Split Animation not beeing written in some cases for the out animation</li>
	<li>Fixed Export to HTML issue with background images creating HTML an error</li>
	<li>Fixed Global CSS not opening</li>
	<li>Fixed rare Slider Import bug</li>
	<li>Fixed border-width not saving correctly in Style Templates</li>
	<li>Fixed warning in not existing pages caused by Slider Revolution</li>
	<li>Fixed the Row / Column  / InnerLayer Timing in Backend and in Frontend</li>
	<li>Split Animation did not change the Timeline Behaviours directly. This has been fixed now</li>
	<li>Hover on SVG Elements was not coloring well the Idle or Hover status has been fixed</li>
	<li>Fixed installation warning that could occur in certain installations</li>
	<li>Fixed layer video cover not showing in Slide Editor for imported Slider</li>
	<li>Fixed Slider Pack import sometimes having wrong orders in created Draft Page</li>
	<li>Layers other than text no longer have "Open Sans" as font-family as default</li>
	<li>Replace Image URL's now properly working with Static Layers</li>
	<li>Fixed a problem where Sliders with Full Slide link in Carousel and in some special mode had issues with the Full Slide Link layer</li>
	<li>Static Layer issues with Invisible Slides where Static Layers were not disappearing on Unvisible Slides on demenad</li>
	<li>Fixed WhiteBoard's multiple "Hand" issue</li>
	<li>Fixed Hover / Idle issues where quick focus/blur events would not remove the Hover animation effects from the layers</li>
</ul>


<h3 class="version-number">Version ******* StarPath (26th October 2016)</h3>
<div class="bugfix"><strong style="font-weight:700">BUGFIXES</strong></div>
<ul>
	<li>Fixed Syntax and Logical Issues like Removing a Slide from Slider based on the Feedback of Sébastien Ledan (http://dotevo.com)! Thank you for the great Feedbacks and Suggestions</li>
	<li>Fixed Slider disappearing in IE11 if Parallax Mode is activated. Double parameter definition in Animation Engine has been fixed</li>
	<li>Fixed an issue where all extension modules should be loaded at once. Folder Name in Path has been renamed from extension to extensions</li>
	<li>Fixed PHP7 issue: "Fatal error: Uncaught Error: [] operator not supported for strings"</li>
	<li>Fixed preview issue where the Slide Editor was not working properly after opening and closing the preview</li>
	<li>Fixed preview in Slide Editor, to show the current changes made without saving the Slide</li>
	<li>Fixed an issue where cross referencing Static Layers were disappeared</li>
	<li>Disappearing elements on Drag & Drop in Slide Editor has been fixed</li>
</ul>


<h3 class="version-number">Version 5.3.0.1 StarPath (22nd October 2016)</h3>
<div class="bugfix"><strong style="font-weight:700">BUGFIXES</strong></div>
<ul>
	<li>Fixed an issue where in Viewpoint Scrolled Slides did not start straight but with a small delay</li>
	<li>Fixed Restarted Static Layers which are animating on each Slide if Start time was smaller than 100ms</li>
	<li>Fixed Warning: Illegal string offset 'width' in [...] object-library.class.php on line 994</li>
	<li>Fixed Global Settings getting reset in certain cases. Which ultimately causes for some customers blank Sliders as the JavaScript files are no longer loaded in the footer</li>
	<li>Fixed bug where actions in Static Layers could be lost after saving</li>
</ul>


<h3 class="version-number">Version 5.3.0 StarPath (21st October 2016)</h3>
<div class="newfeature"><strong style="font-weight:700">NEW FEATURES</strong></div>
<ul>
	<li>New Brasilian/Portuguese translation added! Special thanks to Diego Fernandes</li>
	<li>Introducing Rows and Columns as Layer Groups</li>
	<li>Added Layer Groups for logical and physical grouping of Layers.</li>
	<li>Animations are now allowed in multiple levels for Groups, Rows, Columns and wrapped Layers</li>
	<li>Added Margin for relative Layers (Columns, Rows, Layers in Columns)</li>
	<li>Rows can be sorted within Top, Middle and Bottom Containers</li>
	<li>Added Post ID {{id}} into the Meta List</li>	
	<li>Added Layer Background Images for Text, Column, Row, Group and Shape</li>
	<li>Added Instagram Image Filtering for Slide Backgrounds</li>
	<li>Slides will receive an Auto Height with Rows and Columns</li>
	<li>Introducing Right Click Content Menu on Layers and BG in Slide Editor for quick Edit mode and shortcuts</li>
	<li>Introducing the Object Library with dozens of new Images, BG Images and Objects</li>
	<li>Added Font Icons to Object Library for quicker icon selection within a Text Layer</li>
	<li>Copy/Paste Layer styles easily from one Layer to the other</li>
	<li>Selecting more than one Layer, to move them together, now available</li>
	<li>Introducing logical grouping of Layers</li>
	<li>Added CSS Blend Modes for Text Layer to get some exclusive effects on backgrounds. (Will not work with older IE Browser)</li>
	<li>Easy Editing and Movement of Groups and Layers in Groups</li>
	<li>Added CTRL / CMD + Click function to enable multiple selections</li>
	<li>Added device based level for Spacings like Paddings, Margins, Text-Aligns which can be set now individual based on Responsive Level</li>
	<li>Added Ken Burns Live Editor</li>
	<li>Added option to include all Slider Revolution JavaScript libraries on page load</li>
	<li>Added new option on Slider Import, to be able to create blank pages with the Slider inside</li>
</ul>
<div class="change"><strong style="font-weight:700">CHANGES</strong></div>
<ul>
	<li>Improved loading speed at Slider Overview</li>
	<li>Changed Timeline Interface for better and easier Time Settings</li>
	<li>Add-On Deactivation Button more visual</li>
	<li>Changed Button Hover feature to allow Hover during the Layer Animation already. Direct feedback from Button to customer</li>
	<li>Changed the Layer Animation Core. Introducing Timeline feature which will allow build multiple frames within one Layer Animation</li>
	<li>Improved the Timeline functionality</li>
	<li>Fixed Template and Object Library Loading methods to improve the Loading speed of Background Edit Modes</li>
	<li>Further WPML improvements</li>
	<li>Removed deprecated function, removed with PHP 7</li>
	<li>Padding, Margin and Text Align are now saved for all devices on saving a style</li>
</ul>
<div class="bugfix"><strong style="font-weight:700">BUGFIXES</strong></div>
<ul>
	<li>Improved Google Font loading by removing duplicated inclusions</li>
	<li>Fixed Slot Animation Issues where Slot Amount was ignored in Fade Slots Tranitions</li>
	<li>Fixed issue where Layer On Complete Event was triggered twice on Slide Changes. This had negative influence i.e. on Add Ons like White Board</li>
	<li>Fixed SVG Resizing issues in the Slide Editor after changing SVG Source of an Object Layer</li>
	<li>Fixed Dotted Overlays issues on Slides</li>
	<li>Fixed CTRL+S (CMD+S) Quick save functions</li>
	<li>Fixed Image Size and Aspect Ratio issues in Slide Editor</li>
	<li>Fixed a bug where FullScreen Slider Offset Container parameter was not working with the Carousel Slider Layout</li>
	<li>Fixed Slide Direction Animation on HOrizontal and Vertical direction when slide change has been triggered by Mouse Scroll</li>
	<li>Fixed Ken Burns Issues where Horizontal and Vertical Start Offsets were not implemented well</li>
	<li>YouTube Playback on Mobile Phones lost Focus and lost Video after Slide Change in some rare situations has been fixed</li>
	<li>Fixed a YouTube Bug which was not allow to trigger YouTube Videos via Layer Action on Mobiles</li>
	<li>Fixed Blurry Buttons in YouTube Videos on Mobile Phones</li>
	<li>Issue where Pendulum Easing was ignored in frontend fixed</li>
	<li>Parallax effet on Background Media was not working without Existing Layer in Slide has been fixed</li>
	<li>Facebook Timeline Images did not show under certain circumstances before. Now they do.
</ul>


<h3 class="version-number">Version 5.2.6 StarPath (24th June 2016)</h3>
<div class="change"><strong style="font-weight:700">CHANGES</strong></div>
<ul>
	<li>Deactivating a purchase code will now remove it from the purchase code field</li>
	<li>Changed activation message if purchase code is already registered</li>
</ul>
<div class="bugfix"><strong style="font-weight:700">BUGFIXES</strong></div>
<ul>
	<li>Fixed issue with latest WooCommerce update</li>
	<li>Removed the SVG copy failure notice</li>
</ul>


<h3 class="version-number">Version 5.2.5.4 StarPath (14th June 2016)</h3>
<div class="change"><strong style="font-weight:700">CHANGES</strong></div>
<ul>
	<li>Instagram API Changes (only username search allowed by Instagram, no hashtag search)</li>
	<li>Moved SVG's into the uploads folder</li>
</ul>


<h3 class="version-number">Version 5.2.5.3 StarPath (1st June 2016)</h3>
<div class="newfeature"><strong style="font-weight:700">NEW FEATURES</strong></div>
<ul>
	<li>Added option to modify z-index for layers, can be set in the hover styles</li>
</ul>

<div class="bugfix"><strong style="font-weight:700">BUGFIXES</strong></div>
<ul>
	<li>Fixed update issue where files could not be copied</li>
</ul>


<h3 class="version-number">Version 5.2.5.2 StarPath (24th May 2016)</h3>
<div class="change"><strong style="font-weight:700">CHANGES</strong></div>
<ul>
	<li>Improved user experience with activation, update and templates messages</li>
</ul>

<div class="bugfix"><strong style="font-weight:700">BUGFIXES</strong></div>
<ul>
	<li>Fixed issue with BuddyPress in combination with Visual Composer</li>
</ul>



<h3 class="version-number">Version 5.2.5.1 StarPath (4th May 2016)</h3>
<div class="newfeature"><strong style="font-weight:700">NEW FEATURES</strong></div>
<ul>
	<li>Added IgnoreHeightChange for Android Phones as Fall Back which will avoid the small Jump on Mobile Phones if Location Bar is removed/resized on scroll</li>
	<li>Added Dark/Light BG for Layer Style Selector, which helps to pick the right Style on demand</li>
</ul>

<div class="change"><strong style="font-weight:700">CHANGES</strong></div>
<ul>
	<li>Changed data that are stored in the WordPress options table to reduce pageload</li>
	<li>Changed Vimeo Frogaloop Script Loading Origin from HTTP:// to HTTPS://</li>
	<li>Add-On Installation Problem Handling optimized</li>
</ul>

<div class="bugfix"><strong style="font-weight:700">BUGFIXES</strong></div>
<ul>
	<li>Fixed Action Delay Issues</li>	
	<li>Eliminated Loading issues, where Static Layer Load time was over 5s or Slider was simple not starting due high loading times</li>
</ul>


<h3 class="version-number">Version 5.2.5 StarPath (16th April 2016)</h3>
<div class="newfeature"><strong style="font-weight:700">NEW FEATURES</strong></div>
<ul>
	<li>Added option to set the ID for the Slider which will also set dynamical added container ID's based on the preseted ID</li>
	<li>Delay option added to the Jump To Slide Layer Action</li>
	<li>Template Slider Packs now available</li>
</ul>

<div class="change"><strong style="font-weight:700">CHANGES</strong></div>
<ul>
	<li>Toggle Content on Click event has been extended. Toggle Content without Actions and also on all actions can be added.</li>
	<li>Added further improvements for the Slider Template Library download routine.</li>
</ul>

<div class="bugfix"><strong style="font-weight:700">BUGFIXES</strong></div>
<ul>
	<li>Fixed minHeight settings where entered suffix broke the Slider output.</li>
	<li>Fixed duplication of ID on layers in frontend, caused by duplicating layers in Slide Editor</li>
</ul>



<h3 class="version-number">Version 5.2.4.1 StarPath (08th April 2016)</h3>
<div class="change"><strong style="font-weight:700">CHANGES</strong></div>
<ul>
	<li>Changed HTML5 example URLs as the old ones are no longer available</li>
	<li>Reminder on LayerAction added as solid warning in case Vimeo Video Controls are set to hidden</li>
	<li>Calculation of Wave Loop Effect changed to respect the real X/Y Offsets</li>
</ul>

<div class="bugfix"><strong style="font-weight:700">BUGFIXES</strong></div>
<ul>
	<li>Vimeo Arguments no longer reset on a Vimeo Layer</li>
	<li>Fixed a Bug where 2nd Time Focused Slider with previously played Video started again even if it was not visible currently</li>
	<li>Fixed an issue with Hungarian Translation.  Hungarian Translation is at 50% currently</li>
	<li>Issue with HTML5 Video Next Slide at End on Mobile was skipping a slide after 2nd loop has been fixed.</li>
	<li>Force Rewind available now for YouTube and Vimeo also on Mobile again</li>
	<li>Shortocde Management on pages was not closing the Modal window in some cases. Added a Workaround which checks the last state of the Window in case the default GUI would not be able to close the window</li>
	<li>Fixed the Start Angle Calculation of Wave Loop Effect</li>
	<li>Keyboard Navigation could not be disabled if all other Navigation was disabled</li>
</ul>


<h3 class="version-number">Version 5.2.4 StarPath (01st April 2016)</h3>
<div class="newfeature"><strong style="font-weight:700">NEW FEATURES</strong></div>
<ul>
	<li>French Translation added</li>
</ul>

<div class="bugfix"><strong style="font-weight:700">BUGFIXES</strong></div>
<ul>
	<li>Static Layer Container was written even if no Static Layers were exist. This blocked 3D Parallax Slides from Hovering/Clicking on Elements</li>
	<li>Fixed an issue where layers were disappearing in Google Chrome and other browsers after revisiting the slide</li>
	<li>Fixed an issue with IE11 not beeing able to handle audio layer in Slide Editor</li>
	<li>Fixed a bug where duplicated layers could loose their hover settings</li>
	<li>Fixed a bug with SVG Objects, where styling was only attached if the SVG Object was loading from cache</li>
</ul>


<h3 class="version-number">Version ******* StarPath (18th March 2016)</h3>
<div class="bugfix"><strong style="font-weight:700">BUGFIXES</strong></div>
<ul>
	<li>Fixed output of 1 before the slider in rare cases</li>
	<li>Fixed issue where layers would only show on hover</li>
</ul>

<h3 class="version-number">Version 5.2.3 StarPath (17th March 2016)</h3>
<div class="change"><strong style="font-weight:700">Changes</strong></div>
<ul>
	<li>Improved google font loading by removing not needed multiple fonts</li>
</ul>
<div class="bugfix"><strong style="font-weight:700">BUGFIXES</strong></div>
<ul>
	<li>Fixed conflicts that could occur in combinations with different third party plugins</li>
	<li>Fixed a Viusal Composer conflict</li>
	<li>Fixed Next Slide At End Bug, Loop, and Loop but Keep Slide Progress Bug for Video Backgrounds</li>
</ul>


<h3 class="version-number">Version 5.2.2 StarPath (11th March 2016)</h3>
<div class="bugfix"><strong style="font-weight:700">BUGFIXES</strong></div>
<ul>
	<li>Fixed an issue in Slide Editor where Options were not any more selectable. This is also a Fix for all iussues with "Can not read lenght of Null" Faliure</li>
	<li>Fixed Layer Timing issue where Layer could not start any more at 0ms</li>
	<li>Fixed Start Value issues in Slide Editor where some Value changed from "0" to a predefined Default value</li>
	<li>Fixed an Issue where Layers disappearing after a long time period.</li>
	<li>Fixed Slider / Layer Grid  Based Align options for Navigation Elements</li>
	<li>Fixed calculation errors of navigation elements</li>
	<li>Fixed an issue where slidelinks on carousel slides was not working properly</li>
	<li>Fixed Static Layer container perspective on 3D Parallax Animations. Static Layers and Layers will have now the same View Perspective. To achieve this, Static Layers will always overlay simple Layers in 3D Parallax Mode</li>
	<li>Scrolling by Scroll Bar will also trigger Parallax and Start Slider functions now</li>
	<li>Fixed issues that could occur with older Visual Composer versions</li>
	<li>Fixed a few template store issues where notices and warning occured</li>
</ul>


<h3 class="version-number">Version 5.2.1 StarPath (4th March 2016)</h3>
<div class="bugfix"><strong style="font-weight:700">BUGFIXES</strong></div>
<ul>
	<li>Fixed an Issue for PHP 5.2 or older where the Plugin was not loading on Backend any more</li>
</ul>


<h3 class="version-number">Version 5.2.0 StarPath (3th March 2016)</h3>
<div class="newfeature"><strong style="font-weight:700">NEW FEATURES</strong></div>
<ul>
	<li>Added Navigation Position Aligned By option to allow Navigation element positioned within the Layer Grid also</li>
	<li>Added individual RTL Option to Arrows, Bullets, Tabs and Tumbnails to change the Slider direction and the order of elements</li>
	<li>Added New Layer Type: Object with SVG Icon Library included (over 500+ SVG Icons)</li>
	<li>Added New Layer Type: Audio Layer</li>
	<li>Added Text-Transform Style Option (Uppercase, LowerCase, Capitalize)</li>
	<li>Added Force Inherit Style Changes on Font Size, Font Weight, Font Color and Line Height in Break Point Based Sliders, to inherit changes for other Device Sizes during changes</li>
	<li>Added PlaceHolders and Presets for Navigation Elements to allow Easy Styling on Navigation elements per Slider</li>
	<li>Added Options to change Navigation Element Styling per Slide (i.e. use Light Navigation on Dark Slides and Dark navigation on Light Slides)</li>
	<li>Live Preview of Custom Changes on Navigation Elements</li>
	<li>Added title Attribute to the background images for Slides</li>
	<li>Added option to redownload Slider Templates.</li>
	<li>Added Bulk TimeLine Functions to arrange Layer timings based on predefined batches</li>
	<li>Added Undo & Redo Steps to the Slide Editor</li>
	<li>Version 5.2. is Introducing the Possibilities of using Addons.</li>
	<li>Added Vimeo "No Controls" option to hide the Control Panel of Vime Videos on request</li>
	<li>Added "Pause on Slide" option to stop the Auto Play Progress on certain slides</li>
	<li>Added an option to Wait for revapi.revstart() Method to start the Slider. This allows to start the Slider on request</li>
	<li>Added an global option to trigger database creation</li>
	<li>Added Static Layers Overflow hidden/visible option</li>
	<li>Added option for Post and Stream Sliders to strip fetched content by words/chars</li>
	<li>Added Cache timing option for Social Streams</li>
	<li>Added Global Mute for All Media Action to mute the whole Slider</li>
	<li>Added Invisible Slide option to make Slides only Availblae due Actions and API Functions</li>
</ul>

<div class="change"><strong style="font-weight:700">Changes</strong></div>
<ul>
	<li>Calculation of HTML5 Videos on IE Edge has been changed to respoect the Cover mode in all responsive mode also</li>
	<li>HTML5 Video leaves automaticaly FullScreen Mode when it reach the end of the Video and Next Slide at End selected</li>
	<li>HTML5 Video does not show Cover image if Pause Mode is selected and Video is on FullScreen Mode</li>
	<li>Improvement of the GUI in Slide Editor</li>
	<li>Removed .htaccess file</li>
	<li>Updated Vimeo API</li>
	<li>Images, Videos and Audios will get Line height 0px to avoid position distortions on small elements</li>

</ul>

<div class="bugfix"><strong style="font-weight:700">BUGFIXES</strong></div>
<ul>
	<li>Fixed Parallax Effect if slider is higher than screen height to avoid an odd jump effect</li>
	<li>Fix for limiting the excerpt length in WooCommerce</li>
	<li>Fix for border-radius not working on hover state in certain cases</li>
	<li>Fix for Sharp Right option not saving properly on layers</li>
	<li>Fix for Cover Mode not beeing applied to all devices for image layers in certain cases</li>
	<li>Fixed a minor jQuery bug in Slider Settings on Hover over different options</li>
	<li>Fix for Mouse Scroll Option.  The values on/off were interchanged</li>
	<li>Fixed a bug which would not allow to go to next slide If Show Only Poster on Mobile was selected on HTML5 Videos</li>
	<li>Slotholder and BG Video got own z-index to fix some layer / video covering issues in Carousel mode</li>
	<li>Fixed an issue where SlideLink on RTL environment was not available</li>
	<li>Fixed an issue where export did not work if a newver version of WPML is active</li>
	<li>Fixed Replace URL's in Slider Settings, to check also for actions, videos and more</li>
	<li>Removed loading of jQuery UI libraries if not needed while Visual Composer is installed</li>
	<li>Fixed Scroll Bugs in Layer Timeline Window</li>
	<li>Fixed Timeline Calculation Issues on Backend</li>
	<li>Fixed Warning Issue by leaving the Slider</li>
	<li>Fixed KenBurns issues on RTL Environment</li>
	<li>Fixed Bug with Static Layers if Static Layer was set to only show on Slider Hover</li>	
	<li>Fixed Video Background Loop And Keep Slider Progress Function</li>
	<li>Fixed Shape Resize Issue, where Shape Height lost its last valuse</li>
	<li>Fixed Sorting Order WP Gallery Images when using revslider shortcode wrap</li>
	<li>Fixed YouTube API Issues, (Mute, Loop, Auto Start, Next Slide Bugs due the latest YT Api)</li>
</ul>


<h3 class="version-number">Version 5.1.61 StarPath (28th January 2016)</h3>
<div class="bugfix"><strong style="font-weight:700">BUGFIXES</strong></div>
<ul>
	<li>Fixed import bug for animations</li>
	<li>Fixed output bug for cover images where in some cases it was only beeing applied to Desktop mode</li>
</ul>


<h3 class="version-number">Version 5.1.6 StarPath (5th January 2016)</h3>
<div class="newfeature"><strong style="font-weight:700">NEW FEATURES</strong></div>
<ul>
	<li>Added MouseWheel Scroll option "carousel" to allow infinite MouseWheel scroll on Sliders in both direction. If enabled, slider will not allow page to scroll during wheel functions on Slider Container</li>
</ul>

<div class="change"><strong style="font-weight:700">Changes</strong></div>
<ul>
	<li>Upgraded FontAwesome Library to latest version 4.5.0</li>
	<li>Leaving Slide Editor will now ask to confirm leaving, to prevent data loss if Slide was not saved</li>
	<li>The video type can now be switched on an existing Video Layer</li>
</ul>

<div class="bugfix"><strong style="font-weight:700">BUGFIXES</strong></div>
<ul>
	<li>Out of ViewPort Preloading and Prestarting was calculating the values wrongly. Issue is fixed in this release</li>
	<li>Min amount of Used Fingers for Touch/Scroll/Link was set to 50 as default, which broke any kind of Touch function on Mobiles in version 5.1.5. Issue is fixed now</li>
</ul>


<h3 class="version-number">Version 5.1.5 StarPath (16th December 2015)</h3>
<div class="newfeature"><strong style="font-weight:700">NEW FEATURES</strong></div>
<ul>
	<li>Added "Layer Selectable" option as Default and Layer based, to enable Layer selection on Frontend for visitors (to copy paste text content from Layers)</li>
	<li>Added a new Method "revremoveslide(slideindex)" to remove a slide from the Slider on demand</li>
	<li>Added a new option to each Slide "Hide after Loop" which will remove / Hide 1 Slide from the Slider after x amount of loop of that certain slide</li>
</ul>

<div class="change"><strong style="font-weight:700">Changes</strong></div>
<ul>
	<li>Strengthened Security Posture</li>
	<li>Removed some inline css written on videos/images</li>
	<li>Restricted access to certain features if the user does not have administrator privileges. Can be enabled again by adding: add_filter('revslider_restrict_role', '__return_false');</li>
</ul>

<div class="bugfix"><strong style="font-weight:700">BUGFIXES</strong></div>
<ul>
	<li>Fixed a bug where the background image would be exported (if there was one selected at a time) even if transparent or solid color was selected</li>	
	<li>Fixed PanZoom/KenBurns Disabling on Mobile function</li>	
	<li>Fixed z-index position for From/Till Calendar in Slide Editor</li>
	<li>Fixed Swipe Treshold and Fingers Options which were ignored in earlier version</li>
	<li>Fixed and issue with Static Layer Parallax handling</li>
	<li>Fixed Specific Posts not sorting properly</li>
	<li>Improved Custom Animation save speed in backend</li>
</ul>


<h3 class="version-number">Version 5.1.4 StarPath (28th November 2015)</h3>
<div class="newfeature"><strong style="font-weight:700">NEW FEATURES</strong></div>
<ul>
	<li>Facebook Album now available at the Facebook Source Type</li>
	<li>Instagram Stream can now also be fetched through hashtags</li>
	<li>Added option to import layers from any existing Slide into the current Slide at the "Add Layer" selector</li>
	<li>Added Video Toggle Action</li>
	<li>Added Mute, UnMute, Toggle Mute Action</li>	
	<li>Added Toggle Functions for HTML Layers. Toggled Layers can have two states if any Toggle Action is activated</li>
</ul>

<div class="change"><strong style="font-weight:700">Changes</strong></div>
<ul>
	<li>Specific Posts buttons "Add Popular Posts" and "Add Recent Posts" now append the IDs instead of replacing the current list</li>
	<li>Video will play muted/unmuted based on the last state of the Mute Button at Slide Load</li>
	<li>Changed YouTube default argument ref=0 to rel=0</li>
</ul>

<div class="bugfix"><strong style="font-weight:700">BUGFIXES</strong></div>
<ul>
	<li>Fixed Delete / Backspace issues in CSS Editor textarea</li>
	<li>Fixed animation opacity divided by 100</li>
	<li>Fixed Fullcover / FullWidth sizing issues of Videos in Backend on differnet Layer Grid sizes</li>
	<li>Fixed Video issues, where Play/Stop video button was not triggering HTML5, YouTube and Vimeo videos correctly</li>
	<li>Fixed HTML5 Video Actions</li>
	<li>Fixed video actions where video Start at was set</li>
	<li>Fixed video "start at" and "end at" not resetting in Slide Editor after adding multiple videos</li>
	<li>Fixed Google Fonts not beeing loaded in frontend from Static Layers in some cases</li>
	<li>Fixed a bug where navigation does not replace meta placeholders if the meta data is empty</li>
	<li>Fixed a bug where Layers of one Slider was animated in the wrong timeline if other Slider changed the slide. Some crosscall issue between independent sliders</li>
	<li>Fixed an issue where Videos after Next Slide At End was not rewinded withouth the Force Rewind feature</li>
	<li>Fixed Parallax Flickering in Safari and Google Chrome</li>
</ul>


<h3 class="version-number">Version 5.1.3 StarPath (17th November 2015)</h3>
<div class="bugfix"><strong style="font-weight:700">BUGFIXES</strong></div>
<ul>
	<li>Fixed type:text; that was added in CSS</li>
	<li>Fixed a JavaScript loading issue in backend</li>
	<li>Fixed a typo failure in slide-main-options.php file which caused some jQuery issue in Slider Settings</li>
</ul>


<h3 class="version-number">Version 5.1.2 StarPath (14th November 2015)</h3>

<div class="newfeature"><strong style="font-weight:700">NEW FEATURES</strong></div>
<ul>
	<li>Added a key listener which will allow to close Fullscreen mode with ESC once it has been triggered by FullScreen Action</li>
	<li>Added Keyboard listener to Slide Editor to save the Slider on CTRL+S / CMD+S</li>
	<li>Added Keyboard listener to Slide Editor to delte Layers on Backspace / Delete Key</li>	  
	<li>Added two new Premium Sliders</li>
</ul>

<div class="change"><strong style="font-weight:700">Changes</strong></div>
<ul>
	<li>Removed the need of Username / API key to activate the plugin</li>
</ul>

<div class="bugfix"><strong style="font-weight:700">BUGFIXES</strong></div>
<ul>
	<li>Message "cover image needs to be set for videos" now appearing only if there is really no cover image set</li>
	<li>Fixed HTML5 Start/End Time issues, and HTML5 Seekbar, Play Button issues</li>
	<li>Fixed a strange bug in Safari, where end of the Video the Slider skipped one following slide</li>
	<li>Fixed an issue if focus changed from one layer to an other, the wrong Aniamtion Speed was "shown" in the Animation speed Fields</li>
	<li>Fixed an issue where the slider could not be loaded on localhosts</li>
	<li>Fixed an issue where duplicated elements in Editor got the wrong zIndex position</li>
	<li>Fixed an issue where Deleted layers still have been selected and some fields still keep their values</li>
</ul>


<h3 class="version-number">Version 5.1.1 StarPath (12th November 2015)</h3>

<div class="newfeature"><strong style="font-weight:700">NEW FEATURES</strong></div>
<ul>
	<li>WooCommerce is now officially available!</li>
	<li>Added own Slider Module for Visual Composer called Slider Revolution 5</li>
	<li>Added Quick Slider builder</li>
	<li>Post Based Sliders can now choose between Categories & Tags, Related, Popular, Recent and Next / Previous</li>
	<li>Specific Post Based Sliders can now add Popular and Recent Posts</li>
	<li>Custom build navigations will now be added to the exports and will be also imported at the import process</li>
	<li>New Cursors for Layers: zoom-in & zoom-out</li>	
</ul>

<div class="change"><strong style="font-weight:700">Changes</strong></div>
<ul>
	<li>Made some changes to the GUI</li>
	<li>Changed Success Messages to disappear faster now</li>
	<li>Changed Error Messages to disappear now automatically</li>
</ul>

<div class="bugfix"><strong style="font-weight:700">BUGFIXES</strong></div>
<ul>
	<li>Fixed a very rare bug where a slide may change it's selected background image</li>
	<li>Fixed "number of comments" option not working properly for Post Based Slider</li>
	<li>Fixed hover color not working</li>	
	<li>Fixed multiple bugs for certain servers that could not import Sliders</li>
	<li>Fixed a bug where the toolbar was missing after adding a video layer</li>
	<li>Fixed some CSS Issues</li>
	<li>Fixed Box Slots where Amount of Slots could not be changed</li>
	<li>Fixed a bug where Resize Function was not working after Editing Video Layer</li>
	<li>Fixed layer Source Type bug, not fetching the desired Source Type</li>
	<li>Fixed a bug where an image was printed as array()</li>
	<li>Fixed HTML5 Crop Issues for IE11 and Edge Browsers</li>
	<li>Fixed a bug where Swipe was not available on elements with links</li>
	<li>Fixed a bug where Parallax elements jumped on half scrolled position</li>
</ul>


<h3 class="version-number">Version 5.1 StarPath (19th October 2015)</h3>

<div class="newfeature"><strong style="font-weight:700">NEW FEATURES</strong></div>
<ul>
	<li>WooCommerce is now available!</li>
	<li>Set thumbnail now also as admin Slide Preview image for easier selection in the Slider List / Slides List </li>
	<li>Added Fullscreen Toggle, Go Fullscreen and Exit Fullscreen actions, to allow Slider Revolution to open in Fullscreen from any layout mode</li>
	<li>Added visibility levels to allow hide/show layers via the Visibility Tab also if no Custom Grid Sizes has been set</li>
	<li>Added new Tooltip for z-index Sorting of Elements</li>
	<li>Added new Tooltip for Layer End Animation Toggler</li>
	<li>Added new Google Font Handling - Using any Google Font on layers will auto manage loading of the font</li>
	<li>Added filter revslider_meta_generator to remove the Meta Generator</li>
	<li>Added Tabs to the Metis Navigation</li>
	<li>Added 3D Parallax Feature</li>
	<li>Added new Transitions</li>
</ul>

<div class="change"><strong style="font-weight:700">Changes</strong></div>
<ul>
	<li>Available options for current selected Layer Types are highlighted better in Slide Editor</li>
	<li>New visualisation of Custom Layer Grid Sizes and Presets to make the function more transparent</li>
	<li>Added some CSS changes to avoid Theme overwrites</li>
	<li>Using Toggle/Play/Stop Slider will restart Progress of Slider, even if it is stopped due Stop At Slide options</li>
	<li>Creating a new Slider will now redirect to the Slide Editor</li>
	<li>Removed the requirement of the ZipArchive extension for import/export</li>
	<li>Renamed action to Play/Pause/Stop Slider for more transparencity</li>
	<li>Added confirmation box for layer deletion</li>
</ul>

<div class="bugfix"><strong style="font-weight:700">BUGFIXES</strong></div>
<ul>	
	<li>Fixed a bug with Focus Listener where the Option had no real effect on the Blur/Focus events.</li>		
	<li>Fixed PHP Warnings in case no static-layers.css existing on some Update processes</li>
	<li>Fixed some PHP Warning in WP Backend</li>
	<li>Fixed Google Fonts / Fonts Drop Down issues</li>
	<li>Fixed Arguments not saving for Main Background Videos</li>
	<li>Fixed WPML bug in combinations with jump to slide actions</li>
	<li>Fixed bug where font-style italic was not written properly for some layers</li>
	<li>Fixed Fullwidth/Fullscreen Visual Bug in Slide Editor</li>
	<li>Fixed Shape Loop Animation issue</li>
	<li>Fixed Text Layer Size issue when layer is added after a Shape element</li>
	<li>Fixed Facebook likes count not working properly in certain cases</li>
	<li>Fixed notice issue in update.class.php</li>
	<li>Fixed revscroll() API Method</li>
	<li>Fixed revkill() Method where Removing Slider from the Stage was broken in some situation (i.e. if  Looping Elements or navigation elements were present)</li>
	<li>Fixed a bug where importing a Slider may not change the custom JavaScript revapi to the new Slider ID</li>
</ul>

<h3 class="version-number">Version 5.0.9 StarPath (21th September 2015)</h3>

<div class="newfeature"><strong style="font-weight:700">NEW FEATURES</strong></div>
<ul>
	<li>Added Enable/Disable FullScreen Video Button for YouTube and HTML5 Videos.  Option is available due the Video Settings Panel only in Layer Video Mode.</li>		
	<li>Added % based Border Radius Fields to Element Styling also</li>	
</ul>

<div class="bugfix"><strong style="font-weight:700">BUGFIXES</strong></div>
<ul>	
	<li>Fixed Poster View only on Mobile and Disable Video on Mobile features</li>	
	<li>Fixed Stop on Hover function. It will keep the Slider paused also if Next slide has been called</li>
	<li>Fixed Full Slider Link Issues where next,previous,slide index and Scroll Under Links were not working well</li>	
	<li>Importing Templates with bigger file size will request a bigger TimeOut to avoid Import failures or will give a clear feedback for further Debugging</li>
</ul>

	
<h3 class="version-number">Version 5.0.8.5 StarPath (15th September 2015)</h3>

<div class="bugfix"><strong style="font-weight:700">BUGFIXES</strong></div>
<ul>	
	<li>Fixed Static Layers Position calculation issues.</li>
	<li>Fixed Typo Failure in the Navigation Styling</li>
	<li>Added a Workaround for YouTube Video API's with broken Origin attributes. (i.e. http://www.domain.com vs. http://domain.com)</li>
	<li>Fixed YouTube Cover Video Loop issue</li>
	<li>Fixed an issue where Disable Force FullWidth reseted to "off" after reloading the Slider Settings</li>
	<li>Fixed Next Slide on Focus in FireFox where Slider skipped 1 Slide after Focusing Browser Tab again</li>
	<li>Fixed Update Slider and Install Template from Server issues in some cases</li>
</ul>



<h3 class="version-number">Version 5.0.8 StarPath (11th September 2015)</h3>
<div class="newfeature"><strong style="font-weight:700">NEW FEATURES</strong></div>
<ul>
	<li>FaceBook API 2.4 support</li>	
	<li>Added Disable Force FullWidth for FullScreen Slider, to be able to set FullScreen Slider floated horizontal</li>
</ul>

<div class="change"><strong style="font-weight:700">CHANGES</strong></div>
<ul>
	<li>Changing Slide Main image change the Thumbnails straight to make the identification of Slides in Small view easier.</li>
	<li>Added a new 5 sec Rule which will skip Lazy Loading and Overall Loading processes in case no Answers comes from Server, and process the Slider further</li>
	<li>Added a new Warning in case SSL Certificate is old or not any more active</li>
</ul>

<div class="bugfix"><strong style="font-weight:700">BUGFIXES</strong></div>
<ul>	
	<li>Fixed a bug where Static Images with Links in lazy Load mode was not showing up correctly</li>
	<li>Fixed a bug where Layers were flashing or not visible at all in "Show Layer on Slider Hover" mode</li>	
	<li>Fixed an OutPut bug within css parser where in some situation an error was reported in Console log with Undefined Index</li>
	<li>Fixed a Bug where Layers text coud not be edited any more if Splitted Layer animation was selected</li>
	<li>Fixed Rounded Background issues on layers in FireFox and IE in some situation</li>
	<li>Put the Auto Complete Window of Font Selection in the Front to Overlay the Button Font Selector</li>
	<li>Fixed Some Visual issue in Static Layer Slide Selector</li>
	<li>Fixed FaceBook Stream Issues due Api Changes.</li>
	<li>Fixed a bug where Video Poster Images from HTTPS Pages could not be loaded well</li>
	<li>Fixed a missing closing Tag in HTML5 Video elements which broke IE11</li>
	<li>Fixed an issue where Mobile Sliders stucked when Video "autoplay" was enabled</li>
	<li>Fixed Static Layer Position problems if Outer Left Navigation was enabled</li>
</ul>


<h3 class="version-number">Version 5.0.7 StarPath (5th September 2015)</h3>

<div class="newfeature"><strong style="font-weight:700">NEW FEATURES</strong></div>
<ul>
	<li>Now First Slide can be always the same (Alternative First Slide) even if the rest order of slides are Randomzied</li>	
	<li>Added Requirements & Recommendations Informations to the Slider Overview page to highlight recommended server configurations</li>	
</ul>

<div class="bugfix"><strong style="font-weight:700">BUGFIXES</strong></div>
<ul>	
	<li>Lazy Loading Fixes. Due an internal failure, none of the Lazy Loading option was working. Single, Smart and Full Lazy Loading are available again.</li>
	<li>Fixed a bug where HTML5 Cover Videos in IE did not really cover the slider.</li>
	<li>Fixed default override not working with all available options</li>
	<li>Fixed record not found issue</li>
</ul>


<h3 class="version-number">Version 5.0.6 StarPath (2nd September 2015)</h3>
<div class="newfeature"><strong style="font-weight:700">NEW FEATURES</strong></div>
<ul>
	<li>Added New Layer Scale mode "Cover" and "Stretch" to be able to scale Layer images proportional also over the Layer grid or over the slide</li>	
</ul>
<div class="change"><strong style="font-weight:700">CHANGES</strong></div>
<ul>
	<li>Small adjustments to the Template Store</li>
</ul>
<div class="bugfix"><strong style="font-weight:700">BUGFIXES</strong></div>
<ul>
	<li>Fixed Uncaught TypeError: Cannot read property 'innerHTML' of null</li>
	<li>Fixed actions bug for Post Based Slider where meta like {{link}} did not work properly</li>
	<li>Fixed issue with 2d rotation resulting in styles not written properly on layer</li>
	<li>Fixed issue where YouTube videos could not be loaded on https origins.</li>
	<li>Fixed an issue where Next/Previous Slide Links from Full Slide Link was not processing well</li>
	<li>Fixed text-align always beeing left</li>
	<li>Problem with Layer Name Editing fixed</li>
</ul>


<h3 class="version-number">Version 5.0.5 StarPath (27th August 2015)</h3>
<div class="newfeature"><strong style="font-weight:700">NEW FEATURES</strong></div>
<ul>
	<li>Added "Disable Browser Blur/Focus behavior" option to not stop the Slider in case the broswer tab has been blurred</li>	
	<li>Added a Info Field under Main Background / Source Settings to show the current selected Image URL</li>
	<li>Double Click is now allowed for Editing content of Layers for quicker Content Editor Mode</li>	
	<li>Added a Quick Menu to edit Slider, Go back to Sliders Overview or edit Slides directly</li>
	<li>Added revaddcallback() Method to add Call Back to i.e. the Parallax Module. See Documentation for further instructions</li>
	<li>Added revgetparallaxproc() Method to get the current scroll percentage</li>
	<li>Added Maximum Width option to Slider that are set to Slide Layout - Auto</li>
	<li>Added new Premium and Free Template Store</li>
	<li>Added dropdown into the admin bar in frontend, to be able to quick edit all existing Slider on the current page (Only visible for adminstrative users)</li>
</ul>

<div class="change"><strong style="font-weight:700">CHANGES</strong></div>
<ul>
	<li>Allow to change Title of Slides due simple Enter / Tab also for quick editing Slide Titles</li>
	<li>Adding content to a text box (for the first time) is automatically mirrored in the layer’s ‘caption text’</li>
	<li>Click on the Small Thumbnail in Slider Overview will open the Slide Editor</li> 
	<li>Changed missleading "Video Not Found" to make it more clear for YouTube videos if no thumbnail is set on the video</li>
	<li>Removed option Hide Controls for Vimeo Video Layer as this is not supported by Vimeo</li>
	<li>Minor Layout changes in Slider lists and in Slide Editor</li>
	<li>All Styles are now printed in one single style tag</li>
	<li>Reduced markup size of the Sliders (inline styles reduced)</li>
</ul>

<div class="bugfix"><strong style="font-weight:700">BUGFIXES</strong></div>
<ul>
	<li>Fixed Next Slide on Focus bug</li>
	<li>Fixed a bug that broke the Slider Output when using Spinner "4"</li>
	<li>Fixed RTL Layer Aligns on Frontend. Elements now showing up in the Correct position on RTL Direction also</li>
	<li>Fixed a bug where files relative with special chars under subdomain could not be loaded</li>	
	<li>Fixes the 1px Gap on the left side at some fullscreen and fullwidth Sliders</li>
	<li>Fixed an Editor Bug where Imported Layers could not be edited on some browser</li>
	<li>Fixed import inside of Slider Settings</li>
	<li>Fixed a bug where Responsive Videos on alternative Layer Grids had only a dimension of 100x100px</li>
	<li>Fixed a bug where Videos not act Responsiv in some situation</li>
	<li>Fixed a bug where Parallax Start Position jumps if Site is scrolled at load</li>
	<li>Fixed a missing Static to a function which could cause a notice.</li>
	<li>Fixed a bug where putRevSlider() could result into a fatal error</li>
	<li>Fixed a bug in import process where the revapi text in custom JS was replaced wrong</li>
	<li>Fixed a bug with WPML, where a part of the website would would change the language to something different if a Slider is added to the page</li>
	<li>Fixed JetPack related bugs for compatibility</li>
	<li>Fixed an issue where media files with Parameter attached in file name were not loaded in the Slider</li>
	<li>Fixed an issue related with ShowBiz where in certain circumstances ShowBiz Slider Settings could no longer be opened</li>
	<li>Fixed an issue with TinyMCE not beeing able to add Slider in the Post/Page Editor</li>
	<li>Fixed Visual Composer FrontEnd editor issue</li>
	<li>Fixed issues if Slider is loaded through ajax</li>
	<li>Fixed issues where the Slider did not show up under certain circumstances</li>
</ul>


<h3 class="version-number">Version 5.0.4.1 StarPath (18th August 2015)</h3>
<div class="bugfix"><strong style="font-weight:700">BUGFIXES</strong></div>
<ul>
	<li>Fixed Bugs for WP 4.3</li>
</ul>


<h3 class="version-number">Version 5.0.4 StarPath (12th August 2015)</h3>

<div class="newfeature"><strong style="font-weight:700">NEW FEATURES</strong></div>
<ul>
	<li>Added new Slide Transition - Slide "remove" which is the opposite transition of Slide "over"</li>
	<li>Added start volume for Videos.</li>
	<li>Added Input fields to edit start and end time/speed alternate to the drag and pull functions</li>
	<li>Added option to choose for original image for thumbnails and the specific dimensions set in the Slider Settings -> Navigation -> Thumbs tab</li>
</ul>

<div class="change"><strong style="font-weight:700">CHANGES</strong></div>
<ul>
	<li>Added the usage of attachment_url_to_postid() if WordPress 4.0.0+ is installed</li>
</ul>

<div class="bugfix"><strong style="font-weight:700">BUGFIXES</strong></div>
<ul>
	<li>Added Change Image button at Background Videos for Slides in Post Based Slider</li>
	<li>Shuffle Mode fixed</li>
	<li>Fixed a bug where files could not be loaded from Relative Path in HTTPS enviroment</li>
	<li>Fixed a bug where muted YouTube video still made a sound for 0.1ms</li>
	<li>Fixed a bug where Slider with Slide Link were not loaded well if no layers were added to the slide</li>
</ul>


<h3 class="version-number">Version 5.0.3 StarPath (11th August 2015)</h3>
<div class="bugfix"><strong style="font-weight:700">BUGFIXES</strong></div>
<ul>
	<li>Fixed a bug where layer would not fade out of the stage.</li>
</ul>

	
<h3 class="version-number">Version 5.0.2 StarPath (8th August 2015)</h3>
<div class="newfeature"><strong style="font-weight:700">NEW FEATURES</strong></div>
<ul>
	<li>Added jQuery 2.1.4 and 1.11.3 Support</li>
</ul>

<div class="bugfix"><strong style="font-weight:700">BUGFIXES</strong></div>
<ul>
	<li>Fixed a bug where if Min Height was set, the Slider was broken.</li>
	<li>Fixed a bug where Slide Links did not work without existing layers.</li>
	<li>Fixed some Backend CSS Issues for RTL / LTR View</li>
	<li>Fallback to transparent background image if no correct background image was set</li>
	<li>Missing Template Previews added</li>
	<li>Fixed a Bug where Images with special Char Names were not loaded.</li>
	<li>Fixed a bug where PunchFont Fallbacks are printed in the content not source</li>
	<li>Fixed a bug where PunchFonts could not be edited in Essential Grid if version 5.0+ of Slider Revolution is installed</li>
</ul>


<h3 class="version-number">Version 5.0.1 StarPath (7th August 2015)</h3>
<div class="bugfix"><strong style="font-weight:700">BUGFIXES</strong></div>
<ul>
	<li>Full Slide Link was not available after update on 5.0.0. Sizing issue is fixed now.</li>
</ul>

<h3 class="version-number">Version 5.0 StarPath (6th August 2015)</h3>

<div class="newfeature"><strong style="font-weight:700">The Technology</strong></div>
<ul><li>Our premise is "less is more" and that is reflected in the structure of our components. In order to incorporate so much functionality into our plugins, we make sure everything is build as modular as possible.</li></ul>
<ul>
<li>Fully Responsive & Mobile Specific Features</li>
<li>jQuery 1.7 - jQuery 2.x Supported</li>
<li>Lightning Fast Greensock Animation Engine</li>
<li>Powerful API functions</li>
<li>Smart Font Loading</li>
</ul>
<div class="newfeature"><strong style="font-weight:700">General Options</strong></div>
<ul><li>We want Revolution Slider to be able to fulfill all slide based roles along with special functionality like carousels and hero blocks. If you can‘t find a specific feature, feel free to ask us!</li></ul>
<ul>
<li>All Sizes Possible (Full Responsive + Revolutionary 4 Level Advanced Sizes)</li>
<li>Hero, Carousel and Classic Slider Features</li>
<li>Fullwidth, Fullscreen, Auto Responsive Slider sizes</li>
<li>Unlimited Slider per page</li>
<li>Image BG Cover, Contain, Tiling, Alignment, etc.</li>
<li>WYSIWYG Drag & Drop Editor</li>
<li>Published / Unpublished Slides</li>
<li>Published slides based on predefined Dates</li>
<li>Simple and Advanced Lazy Loading for Quicker and SEO Optimized Slider Start</li>
<li>Link and Actions on Slides</li>
<li>Parallax Effects, full customizeable, combined with Ken Burns and other effects (Mouse / Scroll controlled)</li>
<li>Improved Light weight Ken Burns Effects (easier & faster)</li>
<li>Word Premiere for Advacned Action Building</li>
<li>Build your Social Stream supported Bulk Slider</li>
<li>Easy and quick building based on Slider, Slide and Layer Templates</li>
<li>Performance Monitor and better Performance Suggestions</li>
<li>Viewport based Slide Loading and Progress</li>
<li>Create Slider Defaults, Reset, overwrite single Settings due all slides</li>
<li>Save Slide, Slider, Layer, Animation as Template</li>
</ul>
<div class="newfeature"><strong style="font-weight:700">Layer Capabilities</strong></div>
Layers have evolved from simple layers to become powerful scene building tools!<br/>Drag and Drop, Customize & Animate your way to your perfect slider.
<ul>
<li>Animation Builder</li>
<li>Huge Number of Possible Transitions</li>
<li>Create your custom animations</li>
<li>Set Start / End Time,  Speed, Ease and Effects of any Layers</li>
<li>Show/hide layers on Slider Effects, Events, Actions</li>
<li>Add Unlimited Number of Layers</li>
<li>YouTube, Vimeo, Self-Hosted HTML5 Videos, Shapes, Buttons, Predefined Buttons as Layer</li>
<li>Set actions and links per Layers</li>
<li>Combine Actions due different Layers and slides</li>
<li>Option to Link to a Specific Slide via Layer</li>
<li>Toggle Animation, Classes, video functions via Layers</li>
<li>Variable Layer Image Sizes, full responsive and/or Device Size based</li>
<lI>Design your Layers for different Device sizes after your needs</li>
<li>Option to Hide Layers on Any Devices</li>
</ul>
<div class="newfeature"><strong style="font-weight:700">Slider Navigation</strong></div>
<ul><li>We have implemented almost all navigation types you can think of, which can be aligned anywhere on the stage.<br/>Be in full control with Slider Revolution Navigation!</li></ul>
<ul>
<li>Bullet, Button, Tabs and Thumbnail Navigation, single or mixed mode. Any position like outter,inner, aligned etc.</li>
<li>Left/Right, Top/Bottom Mouse Scroll events.</li> 
<li>Vertical/Horizontal Keyboard actions</li>
<li>Mobile Touch Enabled (Optional)</li>
<li>Drag and Pull Carousel Feature</li>
<li>"Stop Slide Timer on Hover" Function</li>
<li>Auto-Hiding of Navigation with Delay Option</li>
<li>Optional Countdown Timer Line</li>
<li>Set Position, color, size of Time Line</li>
<li>Set Size, visibility, amount and behaviour of Thumbs, Tabs, Bullets, Arrows</li>
<li>Hide / Enable Navigation on Mobile Devices</li>
<li>Keyboard Navigation</li>
<li>Fancy Navigation Skins with Slider Preview</li>
</ul>
<div class="newfeature"><strong style="font-weight:700">Video Features</strong></div>
<ul>
<li>AutoPlay - Always, only first time, skip first time, wait for action</li>
<li>Stop video on Blur, Play Video only in ViewPort</li>
<li>Rewind, or keep current progress time</li>
<li>Set Star and End time</li>
<li>Loop, Loop and Progress Slide</li>
<li>Fullscreen, fullwidth, boxed</li>
<li>Navigation features</li>
<li>Action based controll (due other layers)</li>
<li>New Video API, Events and Methods to controll media outside of the Slider</li>
</ul>
<div class="newfeature"><strong style="font-weight:700">Content Sources</strong></div>
Slider Revolution is not just your ordinary image & video slider any longer. Now you can also pull the sliders content from popular social media steams.
<ul>
<li>Custom-Build Content</li>
<li>WordPress Posts</li>
<li>Facebook</li>
<li>Twitter</li>
<li>YouTube</li>
<li>Vimeo</li>
<li>Flickr</li>
<li>Instagram</li>
<li>(WooCommerce: Coming Soon!)</li>
</ul>
<div class="newfeature"><strong style="font-weight:700">Get Involved!</strong></div>
Is there a feature you would like to see?<br>We will go through all your feedback weekly and pick the most requested features to be included in a future update!<br/>
<a href="http://codecanyon.net/user/themepunch#contact">Contact us via our Profile Form</a><br/><br/><br/>
	
	
	<h3 class="version-number">Version 4.6.93 SkyWood (8th May 2015)</h3>
	<div class="bugfix"><strong style="font-weight:700">BUGFIXES</strong></div>
	<ul>
		<li>Fixed a bug in backend at the process of adding YouTube videos</li>
	</ul>
	
	
	<h3 class="version-number">Version 4.6.92 SkyWood (28th April 2015)</h3>
	<div class="bugfix"><strong style="font-weight:700">BUGFIXES</strong></div>
	<ul>
		<li>Added a fix for PHP version < 5.3 which appeared in version 4.6.91</li>
	</ul>
	
	
	<h3 class="version-number">Version 4.6.91 SkyWood (28th April 2015)</h3>
	<div class="bugfix"><strong style="font-weight:700">BUGFIXES</strong></div>
	<ul>
		<li>Fixed a very low risk vulnerability to stay on par with security standards: More info on the envato blog: http://marketblog.envato.com/news/wordpress-item-security-vulnerability/</li>
	</ul>
	
	
	<h3 class="version-number">Version 4.6.9 SkyWood (16th April 2015)</h3>
	<div class="bugfix"><strong style="font-weight:700">BUGFIXES</strong></div>
	<ul>
		<li>Fixed wrong ajax action: update_slider occuring in 4.6.8</li>
	</ul>
	

	<h3 class="version-number">Version 4.6.8 SkyWood (15th April 2015)</h3>
	<div class="newfeature"><strong style="font-weight:700">INFO</strong></div>
	<ul>
		<li>This update is a preparation for version 5.0</li>
	</ul>
	<div class="bugfix"><strong style="font-weight:700">BUGFIXES</strong></div>
	<ul>
		<li>Fixed Vimeo Event Video Playing which was not triggered</li>
		<li>Fixed Parallax issues where Mouse was already hovering on Slider at load, parallax was not working</li>
		<li>Fixed Parallax "Not clickable" Layers</li>
		<li>Fixed Firefox Half cut images during the Animation sequences</li>
	</ul>
	
	
	<h3 class="version-number">Version 4.6.5 SkyWood (2nd December 2014)</h3>
	<div class="bugfix"><strong style="font-weight:700">BUGFIXES</strong></div>
	<ul>
		<li>Fixed Compatibility of Sandboxed GreenSock engine</li>
		<li>Fixed Compatibility issues with W3C Total Cache Minifying engine</li>
		<li>Fixed Static Layer Disappearing Issue</li>
		<li>Fixed Lazy Loading and Preloading issues (Distorted images in FireFox)</li>
	</ul>
	

	<h3 class="version-number">Version 4.6.4 SkyWood (27th November 2014)</h3>
	<div class="newfeature"><strong style="font-weight:700">NEW FEATURES</strong></div>
	<ul>
		<li>Added option to show only the Preview Image of a video on mobile, disabling the video</li>
	</ul>
	<div class="change"><strong style="font-weight:700">CHANGES</strong></div>
	<ul>
		<li>Min. Height now gone if Fullscreen is selected</li>
	</ul>
	<div class="bugfix"><strong style="font-weight:700">BUGFIXES</strong></div>
	<ul>
		<li>Fixed an issue where the Parallax Effect if Mouse already hovered on the Slider in Firefox was not working well</li>
		<li>Fixed an issue where exports can have wrong styles in it</li>
		<li>Fixed Firefox HTML5 Video Playback</li>
		<li>Fixed Thumbnail Vertical Offset Positions in Resized Screens</li>		
	</ul>
	
	
	<h3 class="version-number">Version 4.6.3 SkyWood (21th October 2014)</h3>
	<div class="bugfix"><strong style="font-weight:700">BUGFIXES</strong></div>
	<ul>
		<li>Fixed an issue where the Slide Link was not working correctly</li>
	</ul>
	
	
	<h3 class="version-number">Version 4.6.2 SkyWood (18th October 2014)</h3>
	<div class="newfeature"><strong style="font-weight:700">NEW FEATURES</strong></div>
	<ul>
		<li>Added "revkill" as method to remove the Slider from the page silently if needed</li>
		<li>Added "slideatend" event that triggers if the slide is at the end</li>
		<li>Changed import function for compatibility with some Windows Server machines (thanks to ThemeFuzz)</li>
		<li>Improved Slider import function for theme authors to make it more easy to insert demo slider through scripts</li>
		<li>Added scripts so that a Slider can be loaded in Essential Grid at Ajax loading (Essential Grid version 1.5 or later needed)</li>
		<li>Added new Custom Controls for HTML5 Video Player to avoid Chrome "unclickable" buttons issue</li>
		<li>Added Rotating Loop Animation on Layers</li>
		<li>Spinner can now be disabled in the Slider Settings</li>
		<li>New fallback options for iOS4 and IE8 added in the Slider Settings (Alternative Image and/or simplyfied output)</li>
		<li>Added option to redraw Slider on browser tab focus</li>
	</ul>
	<div class="change"><strong style="font-weight:700">CHANGES</strong></div>
	<ul>
		<li>More changes on exporting Sliders for further compatibility</li>
		<li>Activating the plugin a second time will now no longer add default fonts again</li>
		<li>Saving CSS / a new CSS class through the style editor will now sanitize the classname</li>
		<li>Setting the single slide setting in the Slide Editor to off will now set the values Arrows Type and Bullets Type to none</li>
		<li>Improved minifying support</li>
		<li>Easy auto settings for single loop option</li>
		<li>Videos on mobile devices available again</li>
		<li>Minimized the styling output</li>
		<li>Improved output validation (style attribute scoped)</li>
		<li>Changed backend icon of static layers to avoid confusion</li>
		<li>Removed console=1 from the YouTube default settings (only works for new added YouTube video layers)</li>
		<li>Google Fonts will no longer be inserted after multiple plugin activation/deactivation</li>
		<li>Improved compatibility with databases</li>
		<li>Enabled unlimited speed parameter</li>
	</ul>
	<div class="bugfix"><strong style="font-weight:700">BUGFIXES</strong></div>
	<ul>
		<li>WPML Slides will now be exported properly and can be imported again</li>
		<li>Improved the size and style handling of Layers in Slide Editor (resizing, rotating and lost values of parameters)</li>
		<li>IE8 - line height "auto" broke the fullscreen slider. In case "auto" is set, the font size +4px will be calculated for line height</li>
		<li>z-index bug for first layer in case dotted overlay exists fixed</li>
		<li>SSL compatibility with Vimeo videos in the backend added</li>
		<li>Chrome's max-width fallback from % to px. Slides accepting now only px values!</li>
		<li>Transition speed had no influence on real transitions</li>
		<li>Max transition can now have the same value as the max delay of the Slide</li>
		<li>Fixed missing Box Slide, Paper Cut and Box Fade transitions</li>
		<li>Further export compatibility for multisites</li>
		<li>Fix of Lists in Captions - Formatting and Display issues</li>	
		<li>Link to Another Slide in Random Mode Bug linked to the wrong Slide Bug is fixed</li>
		<li>Undefined poster image on HTML5 videos fixed</li>
		<li>Fixed Vimeo/YouTube timings (delaying issues)</li>
		<li>Fixed KenBurn dotted overlay</li>
		<li>Fixed problem with loop animations</li>
		<li>Fixed navigation style issues</li>
		<li>Fixed Pan Zoom for mobile issues</li>
		<li>Fixed a bug where videos restarted on resize</li>
		<li>Fixed HTML5 video markup output</li>
		<li>Fixed a bug where hide slider on mobile did not work on certain devices</li>
		<li>Fixed a bug with speed parameters</li>
		<li>Fixed a bug where caption naming in layers list was not updating anymore</li>
		<li>Further IE8 compatibility by changing CSS styles</li>
		<li>Fixed boxfade and boxslide animations</li>
		<li>Fixed a bug where the first layer z-index was always placed behind the dotted overlay</li>
		<li>SSL compatibility with Vimeo API in the backend added</li>
	</ul>


	<h3 class="version-number">Version 4.6 SkyWood (25th August 2014)</h3>
	<div class="newfeature"><strong style="font-weight:700">NEW FEATURES</strong></div>
	<ul>
		<li>Option to disable each video on mobile</li>
		<li>Option to disable Pan Zoom on Mobile</li>
		<li>Option to disable Parallax on Mobile</li>
		<li>Randomized Animation from the Selected Animations available now</li>
		<li>New Offset option for the  Scroll Below Function</li>
		<li>New Option to set Slider Min Height - Content will be vertical centered in case content container is smaller then min height of Slider</li>
		<li>New Loop Options for HTML5 Videos: none, loop and stop Slider Timer or Loop till Progress Bar reaches the End</li>
	</ul>
	<div class="change"><strong style="font-weight:700">CHANGES</strong></div>
	<ul>
		<li>Alternative First Slide wins now, even if Random Slides is enabled</li>
		<li>Vimeo, YouTube and HTML5 Video can be added now via options on demand instead of preadding iFrames.  This will avoid Preloads delays, and slow site loadings</li>
		<li>Using now tp-videolayer class for Videos to identificate Video Layers better</li>
		<li>Class "current-sr-slide-visible" added to the Slide "li" tag which is currently Visible</li>
		<li>Swipe Engine Change</li>
		<li>Swipe Treshold Option Default 75 - The number of pixels that the user must move their finger by before it is considered a swipe.</li>
		<li>Swipe Min Finger Default 1 -  Min Finger (touch) used for swipe</li>
		<li>Drag Block Vertical Default false - Scroll Auto below Slider on Vertical Swipe on Slider</li>
	</ul>
	<div class="bugfix"><strong style="font-weight:700">BUGFIXES</strong></div>
	<ul>
		<li>YouTube Force Rewind fix</li>
		<li>YouTube Mute in Second Loop fix</li>
		<li>Added backwards compatibility for inline CSS with WordPress versions under 3.7</li>
		<li>Ken Burns Dotted Overlay was not visible</li>
		<li>IE8 Console Group Issue - Slider was not visible in IE8</li>
		<li>Looping Issues if Slides has been manually hanged after Loop Stopped</li>
		<li>Browser Tab Change  -> Broken ELements fixed. Option for Slide Change on Blur Tab / Browser also available.</li>
		<li>Navigation Style Preview1- Preview4 Styling issues</li>
		<li>Power2 Error at some Animation</li>
		<li>Mouse Over on Loaded PArallax Slider issue - Slider will always play parallax effect, even if Mouse was already hovering the slider at laod</li>
		<li>IE8 and other Old MObile Devices Fall Back on simple Transitions. CAn be forced now</li>
		<li>HTML5, YouTube and Vimeo Video Playback fixes</li>
	</ul>


	<h3 class="version-number">Version 4.5.96 SkyWood (24th July 2014)</h3>
	<div class="change"><strong style="font-weight:700">CHANGES</strong></div>
	<ul>
		<li>Parallax Mouse Hover acting only in Horizontal Direction if Parallax Scroll is defined</li>
	</ul>
	<div class="bugfix"><strong style="font-weight:700">BUGFIXES</strong></div>
	<ul>
	</ul>


	<h3 class="version-number">Version 4.5.95 SkyWood (16th July 2014)</h3>
	<div class="change"><strong style="font-weight:700">CHANGES</strong></div>
	<ul>
		<li>Changed Animation Engine, improved Animation technology. Reduced file size, and added Timeline based transitions</li>
	</ul>
	<div class="bugfix"><strong style="font-weight:700">BUGFIXES</strong></div>
	<ul>
		<li>Fixed Flickering effects on mobile devices</li>
		<li>Fixed stuttering on animation with many layers</li>
		<li>Fixed Ken Burn animation failures after few Transition types</li>
		<li>Added Safari 3d Back to Transitions</li>
		<li>Fixed some broken Slide Transitions</li>
		<li>Fixed IE8 transition failres, fall back always on Fade</li>
	</ul>


	<h3 class="version-number">Version 4.5.9 SkyWood (7th July 2014)</h3>
	<div class="newfeature"><strong style="font-weight:700">NEW FEATURES</strong></div>
	<ul>
		<li>Drag and Rotate the Layer Content to set a 2D Rotation per element</li>
		<li>Drag and Resize Elements, images, Contents for better text breaks, and more flexibility</li>
		<li>Now also Split Animated Text content can be Rotated</li>
		<li>Added feature to allow Looped Animation and Rotated Content</li>
	</ul>
	<div class="change"><strong style="font-weight:700">CHANGES</strong></div>
	<ul>
		<li>Reenabled Link ID, Class, Title and Rel in Slide Editor</li>
	</ul>
	<div class="bugfix"><strong style="font-weight:700">BUGFIXES</strong></div>
	<ul>
		<li>Fixed Vimeo First Auto Play issue</li>
		<li>Fixed unselectable elements in Locked Mode</li>
		<li>Fixed Output Failure if Rotated and Looped Animation has been selected</li>
		<li>Various Bug fix in Drag And Drop Editor</li>
		<li>Fix of Background Image if Screen size less Then Grid Width, postion of Elements and BG backend and frontend looked different</li>
		<li>Various Fixes for Mobile Styling.  Broken Transitions, bad performance on Mobile devices</li>
		<li>Bug with Rotated Layers on Backend fixed</li>
		<li>Bug with MAx Width and Max Height of elements was not visible in Backend</li>
		<li>White Space Bugs - Backend was not displaying settings of White Spaces</li>
		<li>Ken Burn Images on backend was distorted if Window has been resized</li>
	</ul>

	<h3 class="version-number">Version 4.5.8 SkyWood (4th July 2014)</h3>
	<div class="change"><strong style="font-weight:700">CHANGES</strong></div>
	<ul>
		<li>Reenabled Link ID, Class, Title and Rel in Slide Editor</li>
	</ul>



	<h3 class="version-number">Version 4.5.7 SkyWood (2nd July 2014)</h3>
	<div class="newfeature"><strong style="font-weight:700">NEW FEATURES</strong></div>
	<ul>
		<li>New Options in Global Settings, like Disable/Enable Hammer.js Plugin</li>
		<li>New Developer Option to Disable via the theme the Hammer.js Plugin (Avada Requests)</li>
	</ul>

	<div class="change"><strong style="font-weight:700">CHANGES</strong></div>
	<ul>
		<li>Minimized Style Output in the Document, only used Layer Styles are really loaded</li>
		<li>Changed the Aq_Resize name to prevend conflicts with other themes that also use it and modified code in it</li>
		<li>Updated GreenSock Engine to 12.1 </li>
		<li>Protected Mode for GreenSock for ThemePunch Plugins added. No more conflict between any other plugins / themes and ThemePunch Tools</li>
		<li>Lag Smoothing Enabled</li>
	</ul>

	<div class="bugfix"><strong style="font-weight:700">BUGFIXES</strong></div>
	<ul>
		<li>Bug fixed where Parallax Mode for Mobile Disabled breaks the 2nd and further Slider Positions</li>
	</ul>


	<h3 class="version-number">Version 4.5.6 SkyWood (25th June 2014)</h3>

	<div class="newfeature"><strong style="font-weight:700">NEW FEATURES</strong></div>
	<ul>
		<li>New developer option to disable all notifications and the activation area. More information at <a href="http://www.themepunch.com/home/<USER>/set-revolution-slider-as-theme/" target="_blank">http://www.themepunch.com/home/<USER>/set-revolution-slider-as-theme/</a></li>
	</ul>

	<div class="change"><strong style="font-weight:700">CHANGES</strong></div>
	<ul>
		<li>Added missing fonts from Punch Fonts into the CSS Editor Font Family dropdown</li>
	</ul>

	<div class="bugfix"><strong style="font-weight:700">BUGFIXES</strong></div>
	<ul>
		<li>Fullscreen Layout Without Offset Heights got the Wrong Height</li>
	</ul>


	<h3 class="version-number">Version 4.5.5 SkyWood (24th June 2014)</h3>

	<div class="newfeature"><strong style="font-weight:700">NEW FEATURES</strong></div>
	<ul>
		<li>Option to disable parallax on mobile added</li>
		<li>Option to add an offset in px or % to FullScreen Slider</li>
		<li>Two new Slide transitions: Parallax Vertical and Parallax Horizontal</li>
		<li>Export Slider into HTML. Option availble first when Feature enabled under Global Settings</li>
	</ul>

	<div class="change"><strong style="font-weight:700">CHANGES</strong></div>
	<ul>
		<li>Static Layers now have to be enable in each Slider for usage (can be found in General Settings tab)</li>
		<li>onChange Event delivers the Slide Index and the Slide jQuery Object now</li>
		<li>Global option JavaScript into footer now also adds the revslider calls into the footer</li>
	</ul>

	<div class="bugfix"><strong style="font-weight:700">BUGFIXES</strong></div>
	<ul>
		<li>Lazy Loading bug in combination with Static Layers fixed</li>
		<li>"Hide Slider Under" option did not redraw the Layers after Resize. Bug is fixed</li>
		<li>YouTube Video Bug with Overlay Image on Mobile Devices.</li>
		<li>IE8 and IE9 Slide Link Bug Fixed</li>
		<li>Output Filters Protection fix for "By Compressing Output"</li>
	</ul>


	<h3 class="version-number">Version 4.5.4 SkyWood (16th June 2014)</h3>

	<div class="newfeature"><strong style="font-weight:700">NEW FEATURES</strong></div>
	<ul>
		<li>Static Layers added. Can be found under Slide List (new button called "Edit Static Layers")</li>
		<li>Possibility added to insert id, class, attr and i.e. own data attributes to each specific Slide</li>
	</ul>

	<div class="change"><strong style="font-weight:700">CHANGES</strong></div>
	<ul>
		<li>Inline CSS compressed/minimized now in output</li>
	</ul>

	<div class="bugfix"><strong style="font-weight:700">BUGFIXES</strong></div>
	<ul>
		<li>Layers sometimes not fading out fixed</li>
	</ul>


	<h3 class="version-number">Version 4.5.3 SkyWood (12th June 2014)</h3>

	<div class="bugfix"><strong style="font-weight:700">BUGFIXES</strong></div>
	<ul>
		<li>IE jQuery bugfix</li>
		<li>Responsive Through All Levels fixed where checkbox would be always checked on each element after reload of Slide Editor</li>
	</ul>


	<h3 class="version-number">Version 4.5.2 SkyWood (10th June 2014)</h3>

	<div class="newfeature"><strong style="font-weight:700">NEW FEATURES</strong></div>
	<ul>
		<li>data-parallaxoffset attribute is now available and updated on Scroll.  This value can be interesting for Developers to read current Parallax Offsets of the elements with the jQuery selector .tp-parallax-container  (Possible usage for Blur, Fade, Rotation effects based on the values)</li>
	</ul>

	<div class="bugfix"><strong style="font-weight:700">BUGFIXES</strong></div>
	<ul>
		<li>Thumbnail is not Showing has been fixed</li>
		<li>Choosen Global Settings now correctly set again</li>
		<li>Shortcode dropdown selector will now insert the right shortcode</li>
		<li>Auto Play only First Time on Videos (YouTube and Vimeo) did not work well.</li>
		<li>Imported layers will be stripped of slashes now</li>
	</ul>


	<h3 class="version-number">Version 4.5.01 SkyWood (06th June 2014)</h3>
	<div class="bugfix"><strong style="font-weight:700">BUGFIXES</strong></div>
	<ul>
		<li>Fixed a bug where new Slider & Template Slider button is missing</li>
	</ul>


	<h3 class="version-number">Version 4.5.0 SkyWood (05th June 2014)</h3>
	<div class="newfeature"><strong style="font-weight:700">NEW FEATURES</strong></div>
	<ul>
		<li>Improved Backend Functionality</li>
		<li>Added a timeline based editor for better & easier handling of layer times</li>
		<li>Loop Effect now available. Choose between four loop types for each layer in the Slide Editor under "Layer Animations"</li>
		<li>Parallax Effect now available. Enable it in Slider Settings in the Parallax Tab and set your desired level of each layer in Slide Settings</li>
		<li>Parallax on Mouse Movement Added</li>
		<li>Parallax on Scroll Added with Background fixed or Scrolled Options</li>
		<li>PArallax on Mobile Device Tilt</li>
		<li>qTranslate is now supported in layers</li>
		<li>Added filter hook called 'revslider_get_posts' that can be used to manipulate the WP_Query array</li>
		<li>New Grid/Snap functionality in Slide Editor for better positioning of elements</li>
		<li>Punch Fonts are now included. This should now be used if google fonts need to be loaded through the Slider Revolution plugin</li>
		<li>Option added to not load the Slider on Mobile devices</li>
		<li>2D rotation options added to layers in Slide Editor</li>
		<li>New navigation types called preview1, preview2, preview3, preview4 and custom</li>
		<li>Custom CSS and JavaScript can now be added directly in each Slider</li>
		<li>Placeholder of Slider at Page load will be auto generated. No more Jump Vertically during Slider is loading</li>
		<li>Added Performance Boost optional, where Outgoing Layers first animated before next slide is played. Helps if many layers added to one Slide</li>
		<li>Reburn of Pan Zoom Effect on FullScreen, FullWidth and Boxed layout with a complete new Engine</li>
	</ul>

	<div class="change"><strong style="font-weight:700">CHANGES</strong></div>
	<ul>
		<li>Check if more then one instance of Slider Revolution exists.</li>
		<li>Added support for spaces in putRevSlider("newslider2014","2,10") like putRevSlider("newslider2014","2, homepage")</li>
		<li>Added check if hammer.js is already included</li>
		<li>Added allowfullscreen="true" into YouTube video iFrame output</li>
		<li>Using now Aqua Resizer for thumbnail generation</li>
		<li>Pagination added to the Slide Overview page</li>
		<li>Added Sorting of Slides based on names or Shortcode in Slider Overview page</li>
		<li>Video ID of YouTube and Vimeo can now be changed on editing a layer</li>
		<li>Added notification on post based slider if the template slider does not have any slides created</li>
		<li>Removed the JavaScript inside of the href attribute of buttons from the Slide Editor</li>
	</ul>

	<div class="bugfix"><strong style="font-weight:700">BUGFIXES</strong></div>
	<ul>
		<li>Fixed a bug where Template Sliders would change to normal Sliders</li>
		<li>Fixed a bug where layer positions in slide editor where not correctly calculated if the layer had a padding or margin.</li>
		<li>white-space and max-width/max-height are now correctly shown in Slide Editor</li>
		<li>Fix for importing where \n was translated to n</li>
		<li>Visible Last slide at Loading fixed</li>
		<li>Visible Navigation Elements on Load fixed</li>
	</ul>



	<h3 class="version-number">Version 4.3.8 SkyWood (27th April 2014)</h3>
	<div class="bugfix"><strong style="font-weight:700">BUGFIXES</strong></div>
	<ul>
		<li>BugFix for Shortcode Selector in WordPress 3.9 that happens on toggle between text & visual button</li>
		<li>added px to height css in fullwidth Sliders</li>
	</ul>


	<h3 class="version-number">Version 4.3.7 SkyWood (17th April 2014)</h3>
	<div class="bugfix"><strong style="font-weight:700">BUGFIXES</strong></div>
	<ul>
		<li>Select ShortCodes are now back in Posts on WordPress 3.9</li>
		<li>Ken Burns Dobble Start after changing the slide</li>
	</ul>

	<h3 class="version-number">Version 4.3.6 SkyWood (14th April 2014)</h3>

	<div class="change"><strong style="font-weight:700">CHANGES</strong></div>
	<ul>
		<li>Ken Burns and Pan Zoom Effect Engine Change. Animation based on Img rotations and Zooms, Transitions instead of Background animation. Due this change the Aniamtions become really Smooth and Clean.</li>
	</ul>

	<div class="bugfix"><strong style="font-weight:700">BUGFIXES</strong></div>
	<ul>
		<li>Fixed some compability issues with custom captions</li>
	</ul>

	<h3 class="version-number">Version 4.3.5 SkyWood (09th April 2014)</h3>

	<div class="change"><strong style="font-weight:700">CHANGES</strong></div>
	<ul>
		<li>Decreased Loading Time of Styles due loading styles Inline in Header (based on WordPress Best Practices)</li>
	</ul>

	<div class="bugfix"><strong style="font-weight:700">BUGFIXES</strong></div>
	<ul>
		<li>Fixed Click and Taps bugs on Elements in Slider viewing Slider in Mobile Devices</li>
		<li>Fixed Positions of Videos after leaving Fullscreen Video Playback</li>
		<li>Fixed Element Transitions by clicking Next/previous Slider before Elements has been shown. Currently Not Visible Elements will not show up suddenly any more.</li>
	</ul>

	<hr>


	<h3 class="version-number">Version 4.3.4 SkyWood (07th April 2014)</h3>

	<div class="change"><strong style="font-weight:700">CHANGES</strong></div>
	<ul>
		<li>Changed Backend Style of Style Editor</li>
		<li>Added Swipe Up & Down to scroll below or above the slider on Mobile Devices</li>
	</ul>

	<div class="bugfix"><strong style="font-weight:700">BUGFIXES</strong></div>
	<ul>
		<li>Fixed Color Picker in Backend Editor</li>
	</ul>

	<hr>


	<h3 class="version-number">Version 4.3.3 SkyWood (27th March 2014)</h3>

	<div class="change"><strong style="font-weight:700">CHANGES</strong></div>
	<ul>
		<li>Changed the validation process to meet the envato requirements</li>
	</ul>

	<div class="bugfix"><strong style="font-weight:700">BUGFIXES</strong></div>
	<ul>
		<li>KenBurn effect "End Position" fix if percentage is choosen</li>
	</ul>

	<hr>

	<h3 class="version-number">Version 4.3.2 SkyWood (25th March 2014)</h3>

	<div class="newfeature"><strong style="font-weight:700">NEW FEATURES</strong></div>
	<ul>
		<li>Added Option keyboardNavigation to allow Navigation with left/right arrows</li>
	</ul>

	<div class="change"><strong style="font-weight:700">CHANGES</strong></div>
	<ul>
		<li>Removed video-js plugin and added Browser Native HTML5 Video Player</li>
	</ul>

	<div class="bugfix"><strong style="font-weight:700">BUGFIXES</strong></div>
	<ul>
		<li>Added stripslashes to the import process to the layers</li>
		<li>Added multisite custom css fix</li>
		<li>Fixed HTML5 Video Loading issues</li>
		<li>Fixed Android Device Swipe Feature</li>
	</ul>

	<hr>

	<h3 class="version-number">Version 4.3.1 SkyWood (21th March 2014)</h3>

	<div class="change"><strong style="font-weight:700">CHANGES</strong></div>
	<ul>
		<li>Changed the activation text</li>
	</ul>

	<div class="bugfix"><strong style="font-weight:700">BUGFIXES</strong></div>
	<ul>
		<li>Fix for Thumbnail generating on some installations</li>
	</ul>

	<hr>

	<h3 class="version-number">Version 4.3 SkyWood (18th March 2014)</h3>
	<div class="bugfix"><strong style="font-weight:700">BUGFIXES</strong></div>
	<ul>
		<li>Fix if Slider does not load after Touch Option is Disabled</li>
		<li>MultiSite export fix on some installations.</li>
	</ul>

	<hr>

	<h3 class="version-number">Version 4.2.7 SkyWood (18th March 2014)</h3>
	<div class="newfeature"><strong style="font-weight:700">NEW FEATURES</strong></div>
	<ul>
		<li>Added Options like Swipe Velocity, Swipe Min/Max Touches</li>
		<li>Added Drag Block Vertical option to prevent verticall scroll if needed. Default (false)</li>
	</ul>

	<div class="bugfix"><strong style="font-weight:700">BUGFIXES</strong></div>
	<ul>
		<li>BugFix for FireFox Timer Line - Visibilty of Timer was ignored in Firefox and IE</li>
		<li>BugFix for checking for new updates</li>
		<li>BugFix for event manager, dates will now be correctly translated if they are not english</li>
	</ul>

	<hr>

	<h3 class="version-number">Version 4.2.6 SkyWood (17th March 2014)</h3>
	<div class="newfeature"><strong style="font-weight:700">NEW FEATURES</strong></div>
	<ul>
		<li>New Slider Setting option: Hide Mobile Nav After in Mobile Visibility Tab</li>
	</ul>

	<div class="change"><strong style="font-weight:700">CHANGES</strong></div>
	<ul>
		<li>Calculation of forceFullScreenAlign Positions. Resized Positions are More exact</li>
		<li>Replacement of rel=0 in youtube video layer disabled</li>
		<li>Added Hide Slider under "window width" for all type of Slider Layouts. Slider is stopped / started depends on the Window size also</li>
	</ul>

	<div class="bugfix"><strong style="font-weight:700">BUGFIXES</strong></div>
	<ul>
		<li>Hide Timer Bug has been fixed</li>
		<li>Small multisite export/import adjustments</li>
		<li>Small general export/import adjustments</li>
		<li>Missing static-captions.css text in console on slider preview removed</li>
		<li>YouTube: Hide Controls do now work</li>
		<li>YouTube/Vimeo allow now to insert direct URL or ID</li>
		<li>YouTube rel=0 fix</li>
		<li>YouTube iOS Fix for Replaying videos on 2nd Call</li>
		<li>StopOnHover set to 1 Bug has been fixed</li>
		<li>Backend Speed improvements on some hosters</li>

	</ul>

	<hr>

	<h3 class="version-number">Version 4.2.5 SkyWood (12th March 2014)</h3>
	<div class="bugfix"><strong style="font-weight:700">BUGFIXES</strong></div>
	<ul>
		<li>Slide Link zIndex issues and Slide Link Resizing issues fixed</li>
		<li>Individual Slide Timer has been fixed. Broken since version 4.2.4</li>
	</ul>


	<hr>

	<h3 class="version-number">Version 4.2.4 SkyWood (10th March 2014)</h3>
	<div class="change"><strong style="font-weight:700">CHANGES</strong></div>
	<ul>
		<li>Updated Russian Translation / Language Pack</li>
		<li>Improved Plugin Processes, Removed Script Intervals and Timers for Better CPU Management (30-50% less CPU Usage)</li>
	</ul>


	<hr>

	<h3 class="version-number">Version 4.2.3 SkyWood (3th March 2014)</h3>
	<div class="bugfix"><strong style="font-weight:700">BUGFIXES</strong></div>
	<ul>
		<li>BugFix in Russian Language causing the error "setting layer_caption not found"</li>
	</ul>


	<hr>

	<h3 class="version-number">Version 4.2.2 SkyWood (28th February 2014)</h3>
	<div class="bugfix"><strong style="font-weight:700">BUGFIXES</strong></div>
	<ul>
		<li>In case HTML5 Video Js Loads later then Meta of Video will not fire the Auto Play Option</li>
		<li>Changed update check. In some situations user will not see that a new update is available.</li>
	</ul>

	<hr>

	<h3 class="version-number">Version 4.2.1 SkyWood (27th February 2014)</h3>
	<div class="bugfix"><strong style="font-weight:700">BUGFIXES</strong></div>
	<ul>
		<li>Fixed YouTube Api, changed youtube api load to permanent Https</li>
		<li>Fixed HTML5 Video Preload. A Waiting process added for Video JS and Stream preloads</li>
		<li>Fixed Vimeo API Preloads</li>
		<li>Fixed "Unclickable" Notice for Auto Updates and Premium Support</li>
	</ul>

	<hr>

	<h3 class="version-number">Version 4.2 SkyWood (25th February 2014)</h3>
	<div class="newfeature"><strong style="font-weight:700">NEW FEATURES</strong></div>
	<ul>
		<li>New Activation process added</li>
		<li>New Automatic Updates process added</li>
		<li>Added Char, Word and Line Based Text animation to improve the Visual Experience on the Layer Animations</li>
		<li>Added 5 Different Loader Spinner with Live Preview Selector</li>
		<li>Added a new Shortcode type. Now [rev_slider slider_alias] and also [rev_slider alias="slider_alias"] works.</li>
		<li>Added polish language thanks to Dawid Dudek (www.supportitc.pl)</li>
		<li>Meta Box for RevSlider now available in all Custom Post Types</li>
		<li>Added remove Thumbnail in Slide Editor</li>
		<li>Added white-space, maxwidth and maxheight options</li>
	</ul>

	<div class="change"><strong style="font-weight:700">CHANGES</strong></div>
	<ul>
		<li>Improved Loading Speed. Slider loads over 1-2 sec quicker than before.</li>
		<li>Improved Lazy Loading, to load Images and Layer images on demand. Intelligent Preloading of next slide Elements.</li>
		<li>Removed function mysql_insert_id() that will be deprecated in php version 5.5.x</li>
		<li>Auto Thumbnail Function, small copy of Slider Image, or Colorer BG used as Default.</li>
	</ul>

	<div class="bugfix"><strong style="font-weight:700">BUGFIXES</strong></div>
	<ul>
		<li>Fixed YouTube Api</li>
		<li>CSS editor bugfix where custom styles were set into other choosen layers</li>
		<li>Added missing argument for WPML, this enables WPML functionality in using the specific post feature</li>
		<li>Small adjustment for css editor that caused problems on some installations</li>
		<li>Check if ssl on external jquery library & css include</li>
		<li>Fixed Overlapping layers if Mixed Align Mode used in Slide</li>
		<li>Fixed Pause/Resume function if Navigation elements has been hovered.</li>
		<li>MultiSite Style Settings -  Minor Workaround added till next Major Release</li>
	</ul>

	<hr>



	<h3 class="version-number">Version 4.1.5 SkyWood</h3>
	<div class="newfeature"><strong style="font-weight:700">NEW FEATURES</strong></div>
	<ul>
		<li>Added Chinese translation thanks to lastme (http://www.lastme.com/)</li>
	</ul>
	<div class="change"><strong style="font-weight:700">CHANGES</strong></div>
	<ul>
		<li>Removed function mysql_insert_id() that will be deprecated in php version 5.5.x</li>
	</ul>



	<hr>

	<h3 class="version-number">Version 4.1.4 SkyWood</h3>
	<div class="newfeature"><strong style="font-weight:700">NEW FEATURES</strong></div>
	<ul>
		<li>ID, Class, Title & Rel attribute for Links on images added</li>
		<li>Added files so that google does not index internal files</li>
	</ul>
	<div class="change"><strong style="font-weight:700">CHANGES</strong></div>
	<ul>
		<li>German language files recreated</li>
		<li>instead of a fatal error, the none existence of ZipArchive will now give a more gently message</li>
	</ul>




		<hr>


	<h3 class="version-number">Version 4.1.3 SkyWood</h3>
	<div class="change"><strong style="font-weight:700">CHANGES</strong></div>
	<ul>
		<li>Imported images through slider import create now also the thumbnails</li>
	</ul>
	<div class="bugfix"><strong style="font-weight:700">BUGFIXES</strong></div>
	<ul>
		<li>stripslashes problem in global styles at slider imports fixed</li>
		<li>multisite import of slider now correctly imports images</li>
		<li>selecting thumbnails/video overlays now work on multisites</li>
	</ul>



	<hr>


	<h3 class="version-number">Version 4.1.2 SkyWood</h3>
	<div class="change"><strong style="font-weight:700">CHANGES</strong></div>
	<ul>
		<li>Deleting Posts inside a Post Slider will now trash instead of deleting them</li>
	</ul>
	<div class="bugfix"><strong style="font-weight:700">BUGFIXES</strong></div>
	<ul>
		<li>Fixed a z-index issue where the-events-calendar set the jquery-ui-dialog overlay over the Slider Revolution Dialogs</li>
		<li>Http / Https loading of API’S in the right format depending on the current URL</li>
		<li>Issue solved with the HTML5 Video Sizing</li>
		<li>YouTube Api does not work without the origin=http://yourdomain any more !!
		   <ul>
			   	<li>The slider adds this now automatically on every YouTube video.</li>
			    <li>NOTE: Please note that this only works on live servers and not local installations.</li>
				<li>For example: autoplay will not work on local machines but on your live servers</li>
		   </ul>
		</li>
	</ul>


		<hr>



	<h3 class="version-number">Version 4.1.1 SkyWood</h3>
	<div class="newfeature"><strong style="font-weight:700">NEW FEATURES</strong></div>
	<ul>
		<li>New API Method: rev redraw for Redrawing the current Slide in case, slider is included some animated container.</li>
		<li>New tab in Slider Settings: Reset Options.
			<ul>
				<li>Reset all Transitions of this Slider to a choosen one</li>
				<li>Reset all Transition Durations of this Slider to a choosen one</li>
				<li>New Dropdown on Post/Pages for revslider Shortcodes (found in Visual mode of Editor)</li>
			</ul>
		</li>
	</ul>
	<div class="change"><strong style="font-weight:700">CHANGES</strong></div>
	<ul>
		<li>New button in Slide Edit mode to go back to Slide List of Slider</li>
	</ul>
	<div class="bugfix"><strong style="font-weight:700">BUGFIXES</strong></div>
	<ul>
		<li>Remove of Aspect Ratio and Dotted Overlay if Cover was not choosen in HTML5 Videos</li>
		<li>Fixed a bug on import of Slider where custom arrows might break</li>
		<li>Fixed a bug for Missing static-layers.css </li>
		<li>Fixed FF and IE10 Animation issues</li>
		<li>Fixed Wrong calculated Center positions if Caption has CSS3 Animations associated </li>
	</ul>


	<hr>



	<h3 class="version-number">Version 4.1 SkyWood</h3>
	<div class="newfeature"><strong style="font-weight:700">NEW FEATURES</strong></div>
	<ul>
		<li>Ken Burns Effect (align start/end) bg Fit (start/end), Speed and easing</li>
		<li>Min Height for Fullscreen Sliders</li>
		<li>Fullscreen HTML5 Videos with new functions</li>
		<li>Cover the whole HTML5 Videoscreen (no black borders)</li>
		<li>Dotted overlays on HTML5 Videos with cover & slide backgrounds</li>
		<li>Dotted Overlay on Background Images (global setting)</li>
		<li>Mute option on Videos</li>
		<li>Force Rewind of Videos on slideview</li>
	</ul>
	<div class="change"><strong style="font-weight:700">CHANGES</strong></div>
	<ul>
		<li>Fallback to old export if zip extension is not installed on server</li>
	</ul>
	<div class="bugfix"><strong style="font-weight:700">BUGFIXES</strong></div>
	<ul>
		<li>Post meta tag fix for plugin: Types - Complete Solution for Custom Fields and Types</li>
	</ul>




	<hr>


	<h3 class="version-number">Version 4.0.6 SkyWood</h3>
	<div class="bugfix"><strong style="font-weight:700">BUGFIXES</strong></div>
	<ul>
		<li>Force Fullwidth Offsets</li>
		<li>Added FF Protection for 3d Animated Elements</li>
		<li>Update GreenSock Engine with Protection for FF25 Animations</li>
		<li>IE8 HALO Effect Solved</li>
	</ul>



	<hr>

	<h3 class="version-number">Version 4.0.5 SkyWood</h3>
	<div class="newfeature"><strong style="font-weight:700">NEW FEATURES</strong></div>
	<ul>
		<li>New Background-Image options in slider settings under Appearance Tab</li>
		<li>Usage of placeholder %link% and %meta:somemeta% for Slide Link in General Slide Settings</li>
	</ul>
	<div class="change"><strong style="font-weight:700">CHANGES</strong></div>
	<ul>
		<li>Added fallback for dynamic-layers.css (if not writable, load layers.php instead)</li>
	</ul>
	<div class="bugfix"><strong style="font-weight:700">BUGFIXES</strong></div>
	<ul>
		<li>Fixed a bug where no layers could be added to Slides</li>
		<li>Fixed a bug where Post Sliders do not show the correct thumbnails in navigation</li>
		<li>Fixed a few texts for better multilanguage support</li>
	</ul>


		<hr>



	<h3 class="version-number">Version 4.0.4 SkyWood</h3>
	<div class="bugfix"><strong style="font-weight:700">BUGFIXES</strong></div>
	<ul>
		<li>inserted two missing functions</li>
	</ul>

	<hr>


	<h3 class="version-number">Version 4.0.3 SkyWood</h3>
	<div class="change">CHNAGES</div>
	<ul>
		<li>the option Responsive Through All Levels in layers is now checked as default</li>
		<li>set default background-position of main slide image to center top</li>
	</ul>
	<div class="bugfix"><strong style="font-weight:700">BUGFIXES</strong></div>
	<ul>
		<li>bug in interpreting the css fixed</li>
		<li>fixed a bug that occures on some video layers and that may brake the edit slide page</li>
		<li>fixed a bug with Post Sliders not being shown with the template Slider styles</li>
	</ul>



	<hr>

	<h3 class="version-number">Version 4.0.2 SkyWood</h3>
	<div class="bugfix"><strong style="font-weight:700">BUGFIXES</strong></div>
	<ul>
		<li>WPML was not working correctly, fixed "Wrong Request" bug</li>
	</ul>


	<hr>

	<h3 class="version-number">Version 4.0.1 SkyWood</h3>
	<div class="change"><strong style="font-weight:700">CHANGES</strong></div>
	<ul>
		<li>add revision to css/js files to prevent caching issues</li>
	</ul>



	<hr>

	<h3 class="version-number">Version 4.0 SkyWood</h3>
	<div class="newfeature"><strong style="font-weight:700">NEW FEATURES</strong></div>
	<ul>
		<li>New Live Style Editor (Simple and Advanced)</li>
		<li>Custom Caption Creator</li>
		<li>Live Caption Animation Viewer in Editor</li>
		<li>Export Feature (with Images and Static/Dynamic Styles and with Custom Animations)</li>
		<li>New Main Image Aligns, Repeating and Sizing (like center,center, repeat-x,y,no, cover and container and many more)</li>
		<li>New Layout Viewer ( to see differences between, fullwidth, fullscreen, custom) </li>
		<li>Only Styles are loaded which used in Current Slides</li>
		<li>template slider for post slider added</li>
		<li>allow external images as slide backgrounds</li>
		<li>new options for videos</li>
		<li>many other new options added</li>
		<li>added relpace url's neer export / import slider</li>
		<li>new options for hiding thumbs/bullets/arrows on mobile</li>
		<li>new option force fill width in autoresponsive mode</li>
		<li>id, class, rel tag can be set for each element</li>
		<li>alt tag for images can be set</li>
		<li>layers can now be locked</li>
		<li>layer images can now be resized</li>
		<li>Added new Flat Transitions</li>
		<li>Added FullScreen and FullWidth Forcing in Boxed Themes</li>
		<li>Added Thumbnail for Videos</li>
		<li>Tooltip for Titles</li>
		<li>Shadow Prelayouts</li>
		<li>Move Tabs to Change Slide Order</li>
		<li>Published / Unpublished Slides also Date / Time Dependent</li>
		<li>Loop and Controlless HTML5 Videos</li>
		<li>Play Video only First Time, then Hold</li>
		<li>Linear Resizing of Thumbs</li>
		<li>Unlimited Containers for Offsetting Fullscreen Height </li>
	</ul>
	<div class="change"><strong style="font-weight:700">CHANGES</strong></div>
	<ul>
		<li>Style of Backend in Flat Style</li>
		<li>Much cleaner interface</li>
		<li>Load Plugins and Revolution Slider splitter, dependent on Other Plugins </li>
		<li>Jquery.themepunch.revolution.min.js loads now in the footer (Optional)</li>
		<li>Main transitions are now listed in flat and premium transitions</li>
	</ul>
	<div class="bugfix"><strong style="font-weight:700">BUGFIXES</strong></div>
	<ul>
		<li>many small/mayor bug fixes</li>
		<li>added error messages on some situations in slider by post view.</li>
		<li>fixed css bug that made the screens overlap with wordpress error messages</li>
		<li>fixed image positioning in edit layers js file</li>
		<li>fixed images url's output in https websites</li>
	</ul>
</div>