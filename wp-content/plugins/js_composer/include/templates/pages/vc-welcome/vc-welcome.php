<?php
if ( ! defined( 'ABSPATH' ) ) {
	die( '-1' );
}
?>
<div class="vc_welcome-tab changelog">
	<div class="vc_feature-section-teaser">
		<div>
			<img class="vc-featured-img" src="<?php echo vc_asset_url( 'vc/vc-welcome/vc_5-3/about-page.png' ); ?>"/>

			<h3><?php _e( 'New Interface, Native Colors', 'js_composer' ); ?></h3>

			<p><?php _e( 'A new slim interface of WPBakery Page Builder with native WordPress colors will become your best companion. Enjoy editing your website hassle free via desktop or mobile.', 'js_composer' ); ?></p>
		</div>
	</div>

	<div class="vc_welcome-feature feature-section vc_row">
		<div class="vc_col-xs-4">
			<img class="vc-img-center"
			     src="<?php echo vc_asset_url( 'vc/vc-welcome/vc_5-3/1.png' ); ?>"/>
			<h4><?php _e( 'Native Colors', 'js_composer' ); ?></h4>

			<p><?php _e( 'Default WordPress colors will blend your WordPress and page builder experience in no time.', 'js_composer' ); ?></p>
		</div>
		<div class="vc_col-xs-4">
			<img class="vc-img-center"
			     src="<?php echo vc_asset_url( 'vc/vc-welcome/vc_5-3/2.png' ); ?>"/>
			<h4><?php _e( 'New Icons', 'js_composer' ); ?></h4>

			<p><?php _e( 'Simple and beautiful SVG icons for all our content elements and controls tailored for mobile devices.', 'js_composer' ); ?></p>
		</div>
		<div class="vc_col-xs-4">
			<img class="vc-img-center"
			     src="<?php echo vc_asset_url( 'vc/vc-welcome/vc_5-3/3.png' ); ?>"/>
			<h4><?php _e( 'Icon Library Updates', 'js_composer' ); ?></h4>

			<p><?php _e( 'Add even more icons to your website with our updated icon libraries.', 'js_composer' ); ?></p>
		</div>
	</div>

	<p class="vc-thank-you">Thank you for choosing WPBakery Page Builder,<br/>Michael M, CEO at WPBakery</p>

</div>
