@import "variables";

@font-face {
  font-family: 'vcpb-plugin-icons';
  src:
    url('@{icomoon-font-path}/vcpb-plugin-icons.ttf?i5rhx5') format('truetype'),
    url('@{icomoon-font-path}/vcpb-plugin-icons.woff?i5rhx5') format('woff'),
    url('@{icomoon-font-path}/vcpb-plugin-icons.svg?i5rhx5#vcpb-plugin-icons') format('svg');
  font-weight: normal;
  font-style: normal;
}

.vc-composer-icon {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: 'vcpb-plugin-icons' !important;
  speak: none;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;

  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.vc-c-icon-minimize {
  &:before {
    content: @vc-c-icon-minimize;
  }
}
.vc-c-icon-close {
  &:before {
    content: @vc-c-icon-close;
  }
}
.vc-c-icon-1-6_4-6_1-6 {
  &:before {
    content: @vc-c-icon-1-6_4-6_1-6;
  }
}
.vc-c-icon-add_template {
  &:before {
    content: @vc-c-icon-add_template;
  }
}
.vc-c-icon-arrow_back {
  &:before {
    content: @vc-c-icon-arrow_back;
  }
}
.vc-c-icon-arrow_drop_down {
  &:before {
    content: @vc-c-icon-arrow_drop_down;
  }
}
.vc-c-icon-arrow_drop_up {
  &:before {
    content: @vc-c-icon-arrow_drop_up;
  }
}
.vc-c-icon-arrow_forward {
  &:before {
    content: @vc-c-icon-arrow_forward;
  }
}
.vc-c-icon-check {
  &:before {
    content: @vc-c-icon-check;
  }
}
.vc-c-icon-arrow_upward {
  &:before {
    content: @vc-c-icon-arrow_upward;
  }
}
.vc-c-icon-arrow_downward {
  &:before {
    content: @vc-c-icon-arrow_downward;
  }
}
.vc-c-icon-sync {
  &:before {
    content: @vc-c-icon-sync;
  }
}
.vc-c-icon-search {
  &:before {
    content: @vc-c-icon-search;
  }
}
.vc-c-icon-1-1 {
  &:before {
    content: @vc-c-icon-1-1;
  }
}
.vc-c-icon-1-2_1-2 {
  &:before {
    content: @vc-c-icon-1-2_1-2;
  }
}
.vc-c-icon-1-3_1-3_1-3 {
  &:before {
    content: @vc-c-icon-1-3_1-3_1-3;
  }
}
.vc-c-icon-1-4_1-2_1-4 {
  &:before {
    content: @vc-c-icon-1-4_1-2_1-4;
  }
}
.vc-c-icon-1-4_1-4_1-4_1-4 {
  &:before {
    content: @vc-c-icon-1-4_1-4_1-4_1-4;
  }
}
.vc-c-icon-1-4_3-4 {
  &:before {
    content: @vc-c-icon-1-4_3-4;
  }
}
.vc-c-icon-1-6_1-6_1-6_1-2 {
  &:before {
    content: @vc-c-icon-1-6_1-6_1-6_1-2;
  }
}
.vc-c-icon-1-6_1-6_1-6_1-6_1-6_1-6 {
  &:before {
    content: @vc-c-icon-1-6_1-6_1-6_1-6_1-6_1-6;
  }
}
.vc-c-icon-1-6_2-3_1-6 {
  &:before {
    content: @vc-c-icon-1-6_2-3_1-6;
  }
}
.vc-c-icon-2-3_1-3 {
  &:before {
    content: @vc-c-icon-2-3_1-3;
  }
}
.vc-c-icon-5-6_1-6 {
  &:before {
    content: @vc-c-icon-5-6_1-6;
  }
}
.vc-c-icon-add_element {
  &:before {
    content: @vc-c-icon-add_element;
  }
}
.vc-c-icon-add {
  &:before {
    content: @vc-c-icon-add;
  }
}
.vc-c-icon-cog {
  &:before {
    content: @vc-c-icon-cog;
  }
}
.vc-c-icon-content_copy {
  &:before {
    content: @vc-c-icon-content_copy;
  }
}
.vc-c-icon-delete_empty {
  &:before {
    content: @vc-c-icon-delete_empty;
  }
}
.vc-c-icon-dragndrop {
  &:before {
    content: @vc-c-icon-dragndrop;
  }
}
.vc-c-icon-fullscreen_exit {
  &:before {
    content: @vc-c-icon-fullscreen_exit;
  }
}
.vc-c-icon-fullscreen {
  &:before {
    content: @vc-c-icon-fullscreen;
  }
}
.vc-c-icon-arrow_drop_right {
  &:before {
    content: @vc-c-icon-arrow_drop_right;
  }
}
.vc-c-icon-layout_default {
  &:before {
    content: @vc-c-icon-layout_default;
  }
}
.vc-c-icon-layout_landscape-smartphones {
  &:before {
    content: @vc-c-icon-layout_landscape-smartphones;
  }
}
.vc-c-icon-layout_landscape-tablets {
  &:before {
    content: @vc-c-icon-layout_landscape-tablets;
  }
}
.vc-c-icon-layout_portrait-smartphones {
  &:before {
    content: @vc-c-icon-layout_portrait-smartphones;
  }
}
.vc-c-icon-layout_portrait-tablets {
  &:before {
    content: @vc-c-icon-layout_portrait-tablets;
  }
}
.vc-c-icon-mode_edit {
  &:before {
    content: @vc-c-icon-mode_edit;
  }
}
.vc-c-icon-row_default_fe {
  &:before {
    content: @vc-c-icon-row_default_fe;
  }
}
.vc-c-icon-text-block {
  &:before {
    content: @vc-c-icon-text-block;
  }
}

