<?php
/**
 * States
 *
 * Returns an array of country states. This deprecates and replaces the /states/ directory found in older versions.
 * States /should/ be defined in English and translated native though localisation files.
 * Countries defined with empty arrays have no states.
 *
 * @package WooCommerce/i18n
 * @version 3.6.0
 */

defined( 'ABSPATH' ) || exit;

return array(
	'AF' => array(),
	'AO' => array( // Angola states.
		'BGO' => __( 'Bengo', 'woocommerce' ),
		'BLU' => __( 'Benguela', 'woocommerce' ),
		'BIE' => __( 'Bié', 'woocommerce' ),
		'CAB' => __( 'Cabinda', 'woocommerce' ),
		'CNN' => __( 'Cunene', 'woocommerce' ),
		'HUA' => __( 'Huambo', 'woocommerce' ),
		'HUI' => __( 'Huíla', 'woocommerce' ),
		'CCU' => __( '<PERSON><PERSON><PERSON>', 'woocommerce' ),
		'CNO' => __( 'Kwanza-Norte', 'woocommerce' ),
		'CUS' => __( 'Kwanza-Sul', 'woocommerce' ),
		'LUA' => __( 'Luanda', 'woocommerce' ),
		'LNO' => __( 'Lunda-Norte', 'woocommerce' ),
		'LSU' => __( 'Lunda-Sul', 'woocommerce' ),
		'MAL' => __( 'Malanje', 'woocommerce' ),
		'MOX' => __( 'Moxico', 'woocommerce' ),
		'NAM' => __( 'Namibe', 'woocommerce' ),
		'UIG' => __( 'Uíge', 'woocommerce' ),
		'ZAI' => __( 'Zaire', 'woocommerce' ),
	),
	'AR' => array( // Argentinian provinces.
		'C' => __( 'Ciudad Aut&oacute;noma de Buenos Aires', 'woocommerce' ),
		'B' => __( 'Buenos Aires', 'woocommerce' ),
		'K' => __( 'Catamarca', 'woocommerce' ),
		'H' => __( 'Chaco', 'woocommerce' ),
		'U' => __( 'Chubut', 'woocommerce' ),
		'X' => __( 'C&oacute;rdoba', 'woocommerce' ),
		'W' => __( 'Corrientes', 'woocommerce' ),
		'E' => __( 'Entre R&iacute;os', 'woocommerce' ),
		'P' => __( 'Formosa', 'woocommerce' ),
		'Y' => __( 'Jujuy', 'woocommerce' ),
		'L' => __( 'La Pampa', 'woocommerce' ),
		'F' => __( 'La Rioja', 'woocommerce' ),
		'M' => __( 'Mendoza', 'woocommerce' ),
		'N' => __( 'Misiones', 'woocommerce' ),
		'Q' => __( 'Neuqu&eacute;n', 'woocommerce' ),
		'R' => __( 'R&iacute;o Negro', 'woocommerce' ),
		'A' => __( 'Salta', 'woocommerce' ),
		'J' => __( 'San Juan', 'woocommerce' ),
		'D' => __( 'San Luis', 'woocommerce' ),
		'Z' => __( 'Santa Cruz', 'woocommerce' ),
		'S' => __( 'Santa Fe', 'woocommerce' ),
		'G' => __( 'Santiago del Estero', 'woocommerce' ),
		'V' => __( 'Tierra del Fuego', 'woocommerce' ),
		'T' => __( 'Tucum&aacute;n', 'woocommerce' ),
	),
	'AT' => array(),
	'AU' => array( // Australian states.
		'ACT' => __( 'Australian Capital Territory', 'woocommerce' ),
		'NSW' => __( 'New South Wales', 'woocommerce' ),
		'NT'  => __( 'Northern Territory', 'woocommerce' ),
		'QLD' => __( 'Queensland', 'woocommerce' ),
		'SA'  => __( 'South Australia', 'woocommerce' ),
		'TAS' => __( 'Tasmania', 'woocommerce' ),
		'VIC' => __( 'Victoria', 'woocommerce' ),
		'WA'  => __( 'Western Australia', 'woocommerce' ),
	),
	'AX' => array(),
	'BD' => array( // Bangladeshi states (districts).
		'BD-05' => __( 'Bagerhat', 'woocommerce' ),
		'BD-01' => __( 'Bandarban', 'woocommerce' ),
		'BD-02' => __( 'Barguna', 'woocommerce' ),
		'BD-06' => __( 'Barishal', 'woocommerce' ),
		'BD-07' => __( 'Bhola', 'woocommerce' ),
		'BD-03' => __( 'Bogura', 'woocommerce' ),
		'BD-04' => __( 'Brahmanbaria', 'woocommerce' ),
		'BD-09' => __( 'Chandpur', 'woocommerce' ),
		'BD-10' => __( 'Chattogram', 'woocommerce' ),
		'BD-12' => __( 'Chuadanga', 'woocommerce' ),
		'BD-11' => __( "Cox's Bazar", 'woocommerce' ),
		'BD-08' => __( 'Cumilla', 'woocommerce' ),
		'BD-13' => __( 'Dhaka', 'woocommerce' ),
		'BD-14' => __( 'Dinajpur', 'woocommerce' ),
		'BD-15' => __( 'Faridpur ', 'woocommerce' ),
		'BD-16' => __( 'Feni', 'woocommerce' ),
		'BD-19' => __( 'Gaibandha', 'woocommerce' ),
		'BD-18' => __( 'Gazipur', 'woocommerce' ),
		'BD-17' => __( 'Gopalganj', 'woocommerce' ),
		'BD-20' => __( 'Habiganj', 'woocommerce' ),
		'BD-21' => __( 'Jamalpur', 'woocommerce' ),
		'BD-22' => __( 'Jashore', 'woocommerce' ),
		'BD-25' => __( 'Jhalokati', 'woocommerce' ),
		'BD-23' => __( 'Jhenaidah', 'woocommerce' ),
		'BD-24' => __( 'Joypurhat', 'woocommerce' ),
		'BD-29' => __( 'Khagrachhari', 'woocommerce' ),
		'BD-27' => __( 'Khulna', 'woocommerce' ),
		'BD-26' => __( 'Kishoreganj', 'woocommerce' ),
		'BD-28' => __( 'Kurigram', 'woocommerce' ),
		'BD-30' => __( 'Kushtia', 'woocommerce' ),
		'BD-31' => __( 'Lakshmipur', 'woocommerce' ),
		'BD-32' => __( 'Lalmonirhat', 'woocommerce' ),
		'BD-36' => __( 'Madaripur', 'woocommerce' ),
		'BD-37' => __( 'Magura', 'woocommerce' ),
		'BD-33' => __( 'Manikganj ', 'woocommerce' ),
		'BD-39' => __( 'Meherpur', 'woocommerce' ),
		'BD-38' => __( 'Moulvibazar', 'woocommerce' ),
		'BD-35' => __( 'Munshiganj', 'woocommerce' ),
		'BD-34' => __( 'Mymensingh', 'woocommerce' ),
		'BD-48' => __( 'Naogaon', 'woocommerce' ),
		'BD-43' => __( 'Narail', 'woocommerce' ),
		'BD-40' => __( 'Narayanganj', 'woocommerce' ),
		'BD-42' => __( 'Narsingdi', 'woocommerce' ),
		'BD-44' => __( 'Natore', 'woocommerce' ),
		'BD-45' => __( 'Nawabganj', 'woocommerce' ),
		'BD-41' => __( 'Netrakona', 'woocommerce' ),
		'BD-46' => __( 'Nilphamari', 'woocommerce' ),
		'BD-47' => __( 'Noakhali', 'woocommerce' ),
		'BD-49' => __( 'Pabna', 'woocommerce' ),
		'BD-52' => __( 'Panchagarh', 'woocommerce' ),
		'BD-51' => __( 'Patuakhali', 'woocommerce' ),
		'BD-50' => __( 'Pirojpur', 'woocommerce' ),
		'BD-53' => __( 'Rajbari', 'woocommerce' ),
		'BD-54' => __( 'Rajshahi', 'woocommerce' ),
		'BD-56' => __( 'Rangamati', 'woocommerce' ),
		'BD-55' => __( 'Rangpur', 'woocommerce' ),
		'BD-58' => __( 'Satkhira', 'woocommerce' ),
		'BD-62' => __( 'Shariatpur', 'woocommerce' ),
		'BD-57' => __( 'Sherpur', 'woocommerce' ),
		'BD-59' => __( 'Sirajganj', 'woocommerce' ),
		'BD-61' => __( 'Sunamganj', 'woocommerce' ),
		'BD-60' => __( 'Sylhet', 'woocommerce' ),
		'BD-63' => __( 'Tangail', 'woocommerce' ),
		'BD-64' => __( 'Thakurgaon', 'woocommerce' ),
	),
	'BE' => array(),
	'BG' => array( // Bulgarian states.
		'BG-01' => __( 'Blagoevgrad', 'woocommerce' ),
		'BG-02' => __( 'Burgas', 'woocommerce' ),
		'BG-08' => __( 'Dobrich', 'woocommerce' ),
		'BG-07' => __( 'Gabrovo', 'woocommerce' ),
		'BG-26' => __( 'Haskovo', 'woocommerce' ),
		'BG-09' => __( 'Kardzhali', 'woocommerce' ),
		'BG-10' => __( 'Kyustendil', 'woocommerce' ),
		'BG-11' => __( 'Lovech', 'woocommerce' ),
		'BG-12' => __( 'Montana', 'woocommerce' ),
		'BG-13' => __( 'Pazardzhik', 'woocommerce' ),
		'BG-14' => __( 'Pernik', 'woocommerce' ),
		'BG-15' => __( 'Pleven', 'woocommerce' ),
		'BG-16' => __( 'Plovdiv', 'woocommerce' ),
		'BG-17' => __( 'Razgrad', 'woocommerce' ),
		'BG-18' => __( 'Ruse', 'woocommerce' ),
		'BG-27' => __( 'Shumen', 'woocommerce' ),
		'BG-19' => __( 'Silistra', 'woocommerce' ),
		'BG-20' => __( 'Sliven', 'woocommerce' ),
		'BG-21' => __( 'Smolyan', 'woocommerce' ),
		'BG-23' => __( 'Sofia', 'woocommerce' ),
		'BG-22' => __( 'Sofia-Grad', 'woocommerce' ),
		'BG-24' => __( 'Stara Zagora', 'woocommerce' ),
		'BG-25' => __( 'Targovishte', 'woocommerce' ),
		'BG-03' => __( 'Varna', 'woocommerce' ),
		'BG-04' => __( 'Veliko Tarnovo', 'woocommerce' ),
		'BG-05' => __( 'Vidin', 'woocommerce' ),
		'BG-06' => __( 'Vratsa', 'woocommerce' ),
		'BG-28' => __( 'Yambol', 'woocommerce' ),
	),
	'BH' => array(),
	'BI' => array(),
	'BO' => array( // Bolivian states.
		'B' => __( 'Chuquisaca', 'woocommerce' ),
		'H' => __( 'Beni', 'woocommerce' ),
		'C' => __( 'Cochabamba', 'woocommerce' ),
		'L' => __( 'La Paz', 'woocommerce' ),
		'O' => __( 'Oruro', 'woocommerce' ),
		'N' => __( 'Pando', 'woocommerce' ),
		'P' => __( 'Potosí', 'woocommerce' ),
		'S' => __( 'Santa Cruz', 'woocommerce' ),
		'T' => __( 'Tarija', 'woocommerce' ),
	),
	'BR' => array( // Brazillian states.
		'AC' => __( 'Acre', 'woocommerce' ),
		'AL' => __( 'Alagoas', 'woocommerce' ),
		'AP' => __( 'Amap&aacute;', 'woocommerce' ),
		'AM' => __( 'Amazonas', 'woocommerce' ),
		'BA' => __( 'Bahia', 'woocommerce' ),
		'CE' => __( 'Cear&aacute;', 'woocommerce' ),
		'DF' => __( 'Distrito Federal', 'woocommerce' ),
		'ES' => __( 'Esp&iacute;rito Santo', 'woocommerce' ),
		'GO' => __( 'Goi&aacute;s', 'woocommerce' ),
		'MA' => __( 'Maranh&atilde;o', 'woocommerce' ),
		'MT' => __( 'Mato Grosso', 'woocommerce' ),
		'MS' => __( 'Mato Grosso do Sul', 'woocommerce' ),
		'MG' => __( 'Minas Gerais', 'woocommerce' ),
		'PA' => __( 'Par&aacute;', 'woocommerce' ),
		'PB' => __( 'Para&iacute;ba', 'woocommerce' ),
		'PR' => __( 'Paran&aacute;', 'woocommerce' ),
		'PE' => __( 'Pernambuco', 'woocommerce' ),
		'PI' => __( 'Piau&iacute;', 'woocommerce' ),
		'RJ' => __( 'Rio de Janeiro', 'woocommerce' ),
		'RN' => __( 'Rio Grande do Norte', 'woocommerce' ),
		'RS' => __( 'Rio Grande do Sul', 'woocommerce' ),
		'RO' => __( 'Rond&ocirc;nia', 'woocommerce' ),
		'RR' => __( 'Roraima', 'woocommerce' ),
		'SC' => __( 'Santa Catarina', 'woocommerce' ),
		'SP' => __( 'S&atilde;o Paulo', 'woocommerce' ),
		'SE' => __( 'Sergipe', 'woocommerce' ),
		'TO' => __( 'Tocantins', 'woocommerce' ),
	),
	'CA' => array( // Canadian states.
		'AB' => __( 'Alberta', 'woocommerce' ),
		'BC' => __( 'British Columbia', 'woocommerce' ),
		'MB' => __( 'Manitoba', 'woocommerce' ),
		'NB' => __( 'New Brunswick', 'woocommerce' ),
		'NL' => __( 'Newfoundland and Labrador', 'woocommerce' ),
		'NT' => __( 'Northwest Territories', 'woocommerce' ),
		'NS' => __( 'Nova Scotia', 'woocommerce' ),
		'NU' => __( 'Nunavut', 'woocommerce' ),
		'ON' => __( 'Ontario', 'woocommerce' ),
		'PE' => __( 'Prince Edward Island', 'woocommerce' ),
		'QC' => __( 'Quebec', 'woocommerce' ),
		'SK' => __( 'Saskatchewan', 'woocommerce' ),
		'YT' => __( 'Yukon Territory', 'woocommerce' ),
	),
	'CH' => array( // Cantons of Switzerland.
		'AG' => __( 'Aargau', 'woocommerce' ),
		'AR' => __( 'Appenzell Ausserrhoden', 'woocommerce' ),
		'AI' => __( 'Appenzell Innerrhoden', 'woocommerce' ),
		'BL' => __( 'Basel-Landschaft', 'woocommerce' ),
		'BS' => __( 'Basel-Stadt', 'woocommerce' ),
		'BE' => __( 'Bern', 'woocommerce' ),
		'FR' => __( 'Fribourg', 'woocommerce' ),
		'GE' => __( 'Geneva', 'woocommerce' ),
		'GL' => __( 'Glarus', 'woocommerce' ),
		'GR' => __( 'Graub&uuml;nden', 'woocommerce' ),
		'JU' => __( 'Jura', 'woocommerce' ),
		'LU' => __( 'Luzern', 'woocommerce' ),
		'NE' => __( 'Neuch&acirc;tel', 'woocommerce' ),
		'NW' => __( 'Nidwalden', 'woocommerce' ),
		'OW' => __( 'Obwalden', 'woocommerce' ),
		'SH' => __( 'Schaffhausen', 'woocommerce' ),
		'SZ' => __( 'Schwyz', 'woocommerce' ),
		'SO' => __( 'Solothurn', 'woocommerce' ),
		'SG' => __( 'St. Gallen', 'woocommerce' ),
		'TG' => __( 'Thurgau', 'woocommerce' ),
		'TI' => __( 'Ticino', 'woocommerce' ),
		'UR' => __( 'Uri', 'woocommerce' ),
		'VS' => __( 'Valais', 'woocommerce' ),
		'VD' => __( 'Vaud', 'woocommerce' ),
		'ZG' => __( 'Zug', 'woocommerce' ),
		'ZH' => __( 'Z&uuml;rich', 'woocommerce' ),
	),
	'CN' => array( // Chinese states.
		'CN1'  => __( 'Yunnan / &#20113;&#21335;', 'woocommerce' ),
		'CN2'  => __( 'Beijing / &#21271;&#20140;', 'woocommerce' ),
		'CN3'  => __( 'Tianjin / &#22825;&#27941;', 'woocommerce' ),
		'CN4'  => __( 'Hebei / &#27827;&#21271;', 'woocommerce' ),
		'CN5'  => __( 'Shanxi / &#23665;&#35199;', 'woocommerce' ),
		'CN6'  => __( 'Inner Mongolia / &#20839;&#33945;&#21476;', 'woocommerce' ),
		'CN7'  => __( 'Liaoning / &#36797;&#23425;', 'woocommerce' ),
		'CN8'  => __( 'Jilin / &#21513;&#26519;', 'woocommerce' ),
		'CN9'  => __( 'Heilongjiang / &#40657;&#40857;&#27743;', 'woocommerce' ),
		'CN10' => __( 'Shanghai / &#19978;&#28023;', 'woocommerce' ),
		'CN11' => __( 'Jiangsu / &#27743;&#33487;', 'woocommerce' ),
		'CN12' => __( 'Zhejiang / &#27993;&#27743;', 'woocommerce' ),
		'CN13' => __( 'Anhui / &#23433;&#24509;', 'woocommerce' ),
		'CN14' => __( 'Fujian / &#31119;&#24314;', 'woocommerce' ),
		'CN15' => __( 'Jiangxi / &#27743;&#35199;', 'woocommerce' ),
		'CN16' => __( 'Shandong / &#23665;&#19996;', 'woocommerce' ),
		'CN17' => __( 'Henan / &#27827;&#21335;', 'woocommerce' ),
		'CN18' => __( 'Hubei / &#28246;&#21271;', 'woocommerce' ),
		'CN19' => __( 'Hunan / &#28246;&#21335;', 'woocommerce' ),
		'CN20' => __( 'Guangdong / &#24191;&#19996;', 'woocommerce' ),
		'CN21' => __( 'Guangxi Zhuang / &#24191;&#35199;&#22766;&#26063;', 'woocommerce' ),
		'CN22' => __( 'Hainan / &#28023;&#21335;', 'woocommerce' ),
		'CN23' => __( 'Chongqing / &#37325;&#24198;', 'woocommerce' ),
		'CN24' => __( 'Sichuan / &#22235;&#24029;', 'woocommerce' ),
		'CN25' => __( 'Guizhou / &#36149;&#24030;', 'woocommerce' ),
		'CN26' => __( 'Shaanxi / &#38485;&#35199;', 'woocommerce' ),
		'CN27' => __( 'Gansu / &#29976;&#32899;', 'woocommerce' ),
		'CN28' => __( 'Qinghai / &#38738;&#28023;', 'woocommerce' ),
		'CN29' => __( 'Ningxia Hui / &#23425;&#22799;', 'woocommerce' ),
		'CN30' => __( 'Macau / &#28595;&#38376;', 'woocommerce' ),
		'CN31' => __( 'Tibet / &#35199;&#34255;', 'woocommerce' ),
		'CN32' => __( 'Xinjiang / &#26032;&#30086;', 'woocommerce' ),
	),
	'CZ' => array(),
	'DE' => array(),
	'DK' => array(),
	'EE' => array(),
	'ES' => array( // Spanish states.
		'C'  => __( 'A Coru&ntilde;a', 'woocommerce' ),
		'VI' => __( 'Araba/&Aacute;lava', 'woocommerce' ),
		'AB' => __( 'Albacete', 'woocommerce' ),
		'A'  => __( 'Alicante', 'woocommerce' ),
		'AL' => __( 'Almer&iacute;a', 'woocommerce' ),
		'O'  => __( 'Asturias', 'woocommerce' ),
		'AV' => __( '&Aacute;vila', 'woocommerce' ),
		'BA' => __( 'Badajoz', 'woocommerce' ),
		'PM' => __( 'Baleares', 'woocommerce' ),
		'B'  => __( 'Barcelona', 'woocommerce' ),
		'BU' => __( 'Burgos', 'woocommerce' ),
		'CC' => __( 'C&aacute;ceres', 'woocommerce' ),
		'CA' => __( 'C&aacute;diz', 'woocommerce' ),
		'S'  => __( 'Cantabria', 'woocommerce' ),
		'CS' => __( 'Castell&oacute;n', 'woocommerce' ),
		'CE' => __( 'Ceuta', 'woocommerce' ),
		'CR' => __( 'Ciudad Real', 'woocommerce' ),
		'CO' => __( 'C&oacute;rdoba', 'woocommerce' ),
		'CU' => __( 'Cuenca', 'woocommerce' ),
		'GI' => __( 'Girona', 'woocommerce' ),
		'GR' => __( 'Granada', 'woocommerce' ),
		'GU' => __( 'Guadalajara', 'woocommerce' ),
		'SS' => __( 'Gipuzkoa', 'woocommerce' ),
		'H'  => __( 'Huelva', 'woocommerce' ),
		'HU' => __( 'Huesca', 'woocommerce' ),
		'J'  => __( 'Ja&eacute;n', 'woocommerce' ),
		'LO' => __( 'La Rioja', 'woocommerce' ),
		'GC' => __( 'Las Palmas', 'woocommerce' ),
		'LE' => __( 'Le&oacute;n', 'woocommerce' ),
		'L'  => __( 'Lleida', 'woocommerce' ),
		'LU' => __( 'Lugo', 'woocommerce' ),
		'M'  => __( 'Madrid', 'woocommerce' ),
		'MA' => __( 'M&aacute;laga', 'woocommerce' ),
		'ML' => __( 'Melilla', 'woocommerce' ),
		'MU' => __( 'Murcia', 'woocommerce' ),
		'NA' => __( 'Navarra', 'woocommerce' ),
		'OR' => __( 'Ourense', 'woocommerce' ),
		'P'  => __( 'Palencia', 'woocommerce' ),
		'PO' => __( 'Pontevedra', 'woocommerce' ),
		'SA' => __( 'Salamanca', 'woocommerce' ),
		'TF' => __( 'Santa Cruz de Tenerife', 'woocommerce' ),
		'SG' => __( 'Segovia', 'woocommerce' ),
		'SE' => __( 'Sevilla', 'woocommerce' ),
		'SO' => __( 'Soria', 'woocommerce' ),
		'T'  => __( 'Tarragona', 'woocommerce' ),
		'TE' => __( 'Teruel', 'woocommerce' ),
		'TO' => __( 'Toledo', 'woocommerce' ),
		'V'  => __( 'Valencia', 'woocommerce' ),
		'VA' => __( 'Valladolid', 'woocommerce' ),
		'BI' => __( 'Bizkaia', 'woocommerce' ),
		'ZA' => __( 'Zamora', 'woocommerce' ),
		'Z'  => __( 'Zaragoza', 'woocommerce' ),
	),
	'FI' => array(),
	'FR' => array(),
	'GP' => array(),
	'GR' => array( // Greek Regions.
		'I' => __( 'Αττική', 'woocommerce' ),
		'A' => __( 'Ανατολική Μακεδονία και Θράκη', 'woocommerce' ),
		'B' => __( 'Κεντρική Μακεδονία', 'woocommerce' ),
		'C' => __( 'Δυτική Μακεδονία', 'woocommerce' ),
		'D' => __( 'Ήπειρος', 'woocommerce' ),
		'E' => __( 'Θεσσαλία', 'woocommerce' ),
		'F' => __( 'Ιόνιοι Νήσοι', 'woocommerce' ),
		'G' => __( 'Δυτική Ελλάδα', 'woocommerce' ),
		'H' => __( 'Στερεά Ελλάδα', 'woocommerce' ),
		'J' => __( 'Πελοπόννησος', 'woocommerce' ),
		'K' => __( 'Βόρειο Αιγαίο', 'woocommerce' ),
		'L' => __( 'Νότιο Αιγαίο', 'woocommerce' ),
		'M' => __( 'Κρήτη', 'woocommerce' ),
	),
	'GF' => array(),
	'HK' => array( // Hong Kong states.
		'HONG KONG'       => __( 'Hong Kong Island', 'woocommerce' ),
		'KOWLOON'         => __( 'Kowloon', 'woocommerce' ),
		'NEW TERRITORIES' => __( 'New Territories', 'woocommerce' ),
	),
	'HU' => array( // Hungary states.
		'BK' => __( 'Bács-Kiskun', 'woocommerce' ),
		'BE' => __( 'Békés', 'woocommerce' ),
		'BA' => __( 'Baranya', 'woocommerce' ),
		'BZ' => __( 'Borsod-Abaúj-Zemplén', 'woocommerce' ),
		'BU' => __( 'Budapest', 'woocommerce' ),
		'CS' => __( 'Csongrád', 'woocommerce' ),
		'FE' => __( 'Fejér', 'woocommerce' ),
		'GS' => __( 'Győr-Moson-Sopron', 'woocommerce' ),
		'HB' => __( 'Hajdú-Bihar', 'woocommerce' ),
		'HE' => __( 'Heves', 'woocommerce' ),
		'JN' => __( 'Jász-Nagykun-Szolnok', 'woocommerce' ),
		'KE' => __( 'Komárom-Esztergom', 'woocommerce' ),
		'NO' => __( 'Nógrád', 'woocommerce' ),
		'PE' => __( 'Pest', 'woocommerce' ),
		'SO' => __( 'Somogy', 'woocommerce' ),
		'SZ' => __( 'Szabolcs-Szatmár-Bereg', 'woocommerce' ),
		'TO' => __( 'Tolna', 'woocommerce' ),
		'VA' => __( 'Vas', 'woocommerce' ),
		'VE' => __( 'Veszprém', 'woocommerce' ),
		'ZA' => __( 'Zala', 'woocommerce' ),
	),
	'ID' => array( // Indonesia Provinces.
		'AC' => __( 'Daerah Istimewa Aceh', 'woocommerce' ),
		'SU' => __( 'Sumatera Utara', 'woocommerce' ),
		'SB' => __( 'Sumatera Barat', 'woocommerce' ),
		'RI' => __( 'Riau', 'woocommerce' ),
		'KR' => __( 'Kepulauan Riau', 'woocommerce' ),
		'JA' => __( 'Jambi', 'woocommerce' ),
		'SS' => __( 'Sumatera Selatan', 'woocommerce' ),
		'BB' => __( 'Bangka Belitung', 'woocommerce' ),
		'BE' => __( 'Bengkulu', 'woocommerce' ),
		'LA' => __( 'Lampung', 'woocommerce' ),
		'JK' => __( 'DKI Jakarta', 'woocommerce' ),
		'JB' => __( 'Jawa Barat', 'woocommerce' ),
		'BT' => __( 'Banten', 'woocommerce' ),
		'JT' => __( 'Jawa Tengah', 'woocommerce' ),
		'JI' => __( 'Jawa Timur', 'woocommerce' ),
		'YO' => __( 'Daerah Istimewa Yogyakarta', 'woocommerce' ),
		'BA' => __( 'Bali', 'woocommerce' ),
		'NB' => __( 'Nusa Tenggara Barat', 'woocommerce' ),
		'NT' => __( 'Nusa Tenggara Timur', 'woocommerce' ),
		'KB' => __( 'Kalimantan Barat', 'woocommerce' ),
		'KT' => __( 'Kalimantan Tengah', 'woocommerce' ),
		'KI' => __( 'Kalimantan Timur', 'woocommerce' ),
		'KS' => __( 'Kalimantan Selatan', 'woocommerce' ),
		'KU' => __( 'Kalimantan Utara', 'woocommerce' ),
		'SA' => __( 'Sulawesi Utara', 'woocommerce' ),
		'ST' => __( 'Sulawesi Tengah', 'woocommerce' ),
		'SG' => __( 'Sulawesi Tenggara', 'woocommerce' ),
		'SR' => __( 'Sulawesi Barat', 'woocommerce' ),
		'SN' => __( 'Sulawesi Selatan', 'woocommerce' ),
		'GO' => __( 'Gorontalo', 'woocommerce' ),
		'MA' => __( 'Maluku', 'woocommerce' ),
		'MU' => __( 'Maluku Utara', 'woocommerce' ),
		'PA' => __( 'Papua', 'woocommerce' ),
		'PB' => __( 'Papua Barat', 'woocommerce' ),
	),
	'IE' => array( // Republic of Ireland.
		'CW' => __( 'Carlow', 'woocommerce' ),
		'CN' => __( 'Cavan', 'woocommerce' ),
		'CE' => __( 'Clare', 'woocommerce' ),
		'CO' => __( 'Cork', 'woocommerce' ),
		'DL' => __( 'Donegal', 'woocommerce' ),
		'D'  => __( 'Dublin', 'woocommerce' ),
		'G'  => __( 'Galway', 'woocommerce' ),
		'KY' => __( 'Kerry', 'woocommerce' ),
		'KE' => __( 'Kildare', 'woocommerce' ),
		'KK' => __( 'Kilkenny', 'woocommerce' ),
		'LS' => __( 'Laois', 'woocommerce' ),
		'LM' => __( 'Leitrim', 'woocommerce' ),
		'LK' => __( 'Limerick', 'woocommerce' ),
		'LD' => __( 'Longford', 'woocommerce' ),
		'LH' => __( 'Louth', 'woocommerce' ),
		'MO' => __( 'Mayo', 'woocommerce' ),
		'MH' => __( 'Meath', 'woocommerce' ),
		'MN' => __( 'Monaghan', 'woocommerce' ),
		'OY' => __( 'Offaly', 'woocommerce' ),
		'RN' => __( 'Roscommon', 'woocommerce' ),
		'SO' => __( 'Sligo', 'woocommerce' ),
		'TA' => __( 'Tipperary', 'woocommerce' ),
		'WD' => __( 'Waterford', 'woocommerce' ),
		'WH' => __( 'Westmeath', 'woocommerce' ),
		'WX' => __( 'Wexford', 'woocommerce' ),
		'WW' => __( 'Wicklow', 'woocommerce' ),
	),
	'IN' => array( // Indian states.
		'AP' => __( 'Andhra Pradesh', 'woocommerce' ),
		'AR' => __( 'Arunachal Pradesh', 'woocommerce' ),
		'AS' => __( 'Assam', 'woocommerce' ),
		'BR' => __( 'Bihar', 'woocommerce' ),
		'CT' => __( 'Chhattisgarh', 'woocommerce' ),
		'GA' => __( 'Goa', 'woocommerce' ),
		'GJ' => __( 'Gujarat', 'woocommerce' ),
		'HR' => __( 'Haryana', 'woocommerce' ),
		'HP' => __( 'Himachal Pradesh', 'woocommerce' ),
		'JK' => __( 'Jammu and Kashmir', 'woocommerce' ),
		'JH' => __( 'Jharkhand', 'woocommerce' ),
		'KA' => __( 'Karnataka', 'woocommerce' ),
		'KL' => __( 'Kerala', 'woocommerce' ),
		'MP' => __( 'Madhya Pradesh', 'woocommerce' ),
		'MH' => __( 'Maharashtra', 'woocommerce' ),
		'MN' => __( 'Manipur', 'woocommerce' ),
		'ML' => __( 'Meghalaya', 'woocommerce' ),
		'MZ' => __( 'Mizoram', 'woocommerce' ),
		'NL' => __( 'Nagaland', 'woocommerce' ),
		'OR' => __( 'Orissa', 'woocommerce' ),
		'PB' => __( 'Punjab', 'woocommerce' ),
		'RJ' => __( 'Rajasthan', 'woocommerce' ),
		'SK' => __( 'Sikkim', 'woocommerce' ),
		'TN' => __( 'Tamil Nadu', 'woocommerce' ),
		'TS' => __( 'Telangana', 'woocommerce' ),
		'TR' => __( 'Tripura', 'woocommerce' ),
		'UK' => __( 'Uttarakhand', 'woocommerce' ),
		'UP' => __( 'Uttar Pradesh', 'woocommerce' ),
		'WB' => __( 'West Bengal', 'woocommerce' ),
		'AN' => __( 'Andaman and Nicobar Islands', 'woocommerce' ),
		'CH' => __( 'Chandigarh', 'woocommerce' ),
		'DN' => __( 'Dadra and Nagar Haveli', 'woocommerce' ),
		'DD' => __( 'Daman and Diu', 'woocommerce' ),
		'DL' => __( 'Delhi', 'woocommerce' ),
		'LD' => __( 'Lakshadeep', 'woocommerce' ),
		'PY' => __( 'Pondicherry (Puducherry)', 'woocommerce' ),
	),
	'IR' => array( // Iran States.
		'KHZ' => __( 'Khuzestan  (خوزستان)', 'woocommerce' ),
		'THR' => __( 'Tehran  (تهران)', 'woocommerce' ),
		'ILM' => __( 'Ilaam (ایلام)', 'woocommerce' ),
		'BHR' => __( 'Bushehr (بوشهر)', 'woocommerce' ),
		'ADL' => __( 'Ardabil (اردبیل)', 'woocommerce' ),
		'ESF' => __( 'Isfahan (اصفهان)', 'woocommerce' ),
		'YZD' => __( 'Yazd (یزد)', 'woocommerce' ),
		'KRH' => __( 'Kermanshah (کرمانشاه)', 'woocommerce' ),
		'KRN' => __( 'Kerman (کرمان)', 'woocommerce' ),
		'HDN' => __( 'Hamadan (همدان)', 'woocommerce' ),
		'GZN' => __( 'Ghazvin (قزوین)', 'woocommerce' ),
		'ZJN' => __( 'Zanjan (زنجان)', 'woocommerce' ),
		'LRS' => __( 'Luristan (لرستان)', 'woocommerce' ),
		'ABZ' => __( 'Alborz (البرز)', 'woocommerce' ),
		'EAZ' => __( 'East Azarbaijan (آذربایجان شرقی)', 'woocommerce' ),
		'WAZ' => __( 'West Azarbaijan (آذربایجان غربی)', 'woocommerce' ),
		'CHB' => __( 'Chaharmahal and Bakhtiari (چهارمحال و بختیاری)', 'woocommerce' ),
		'SKH' => __( 'South Khorasan (خراسان جنوبی)', 'woocommerce' ),
		'RKH' => __( 'Razavi Khorasan (خراسان رضوی)', 'woocommerce' ),
		'NKH' => __( 'North Khorasan (خراسان شمالی)', 'woocommerce' ),
		'SMN' => __( 'Semnan (سمنان)', 'woocommerce' ),
		'FRS' => __( 'Fars (فارس)', 'woocommerce' ),
		'QHM' => __( 'Qom (قم)', 'woocommerce' ),
		'KRD' => __( 'Kurdistan / کردستان)', 'woocommerce' ),
		'KBD' => __( 'Kohgiluyeh and BoyerAhmad (کهگیلوییه و بویراحمد)', 'woocommerce' ),
		'GLS' => __( 'Golestan (گلستان)', 'woocommerce' ),
		'GIL' => __( 'Gilan (گیلان)', 'woocommerce' ),
		'MZN' => __( 'Mazandaran (مازندران)', 'woocommerce' ),
		'MKZ' => __( 'Markazi (مرکزی)', 'woocommerce' ),
		'HRZ' => __( 'Hormozgan (هرمزگان)', 'woocommerce' ),
		'SBN' => __( 'Sistan and Baluchestan (سیستان و بلوچستان)', 'woocommerce' ),
	),
	'IS' => array(),
	'IT' => array( // Italy Provinces.
		'AG' => __( 'Agrigento', 'woocommerce' ),
		'AL' => __( 'Alessandria', 'woocommerce' ),
		'AN' => __( 'Ancona', 'woocommerce' ),
		'AO' => __( 'Aosta', 'woocommerce' ),
		'AR' => __( 'Arezzo', 'woocommerce' ),
		'AP' => __( 'Ascoli Piceno', 'woocommerce' ),
		'AT' => __( 'Asti', 'woocommerce' ),
		'AV' => __( 'Avellino', 'woocommerce' ),
		'BA' => __( 'Bari', 'woocommerce' ),
		'BT' => __( 'Barletta-Andria-Trani', 'woocommerce' ),
		'BL' => __( 'Belluno', 'woocommerce' ),
		'BN' => __( 'Benevento', 'woocommerce' ),
		'BG' => __( 'Bergamo', 'woocommerce' ),
		'BI' => __( 'Biella', 'woocommerce' ),
		'BO' => __( 'Bologna', 'woocommerce' ),
		'BZ' => __( 'Bolzano', 'woocommerce' ),
		'BS' => __( 'Brescia', 'woocommerce' ),
		'BR' => __( 'Brindisi', 'woocommerce' ),
		'CA' => __( 'Cagliari', 'woocommerce' ),
		'CL' => __( 'Caltanissetta', 'woocommerce' ),
		'CB' => __( 'Campobasso', 'woocommerce' ),
		'CE' => __( 'Caserta', 'woocommerce' ),
		'CT' => __( 'Catania', 'woocommerce' ),
		'CZ' => __( 'Catanzaro', 'woocommerce' ),
		'CH' => __( 'Chieti', 'woocommerce' ),
		'CO' => __( 'Como', 'woocommerce' ),
		'CS' => __( 'Cosenza', 'woocommerce' ),
		'CR' => __( 'Cremona', 'woocommerce' ),
		'KR' => __( 'Crotone', 'woocommerce' ),
		'CN' => __( 'Cuneo', 'woocommerce' ),
		'EN' => __( 'Enna', 'woocommerce' ),
		'FM' => __( 'Fermo', 'woocommerce' ),
		'FE' => __( 'Ferrara', 'woocommerce' ),
		'FI' => __( 'Firenze', 'woocommerce' ),
		'FG' => __( 'Foggia', 'woocommerce' ),
		'FC' => __( 'Forlì-Cesena', 'woocommerce' ),
		'FR' => __( 'Frosinone', 'woocommerce' ),
		'GE' => __( 'Genova', 'woocommerce' ),
		'GO' => __( 'Gorizia', 'woocommerce' ),
		'GR' => __( 'Grosseto', 'woocommerce' ),
		'IM' => __( 'Imperia', 'woocommerce' ),
		'IS' => __( 'Isernia', 'woocommerce' ),
		'SP' => __( 'La Spezia', 'woocommerce' ),
		'AQ' => __( "L'Aquila", 'woocommerce' ),
		'LT' => __( 'Latina', 'woocommerce' ),
		'LE' => __( 'Lecce', 'woocommerce' ),
		'LC' => __( 'Lecco', 'woocommerce' ),
		'LI' => __( 'Livorno', 'woocommerce' ),
		'LO' => __( 'Lodi', 'woocommerce' ),
		'LU' => __( 'Lucca', 'woocommerce' ),
		'MC' => __( 'Macerata', 'woocommerce' ),
		'MN' => __( 'Mantova', 'woocommerce' ),
		'MS' => __( 'Massa-Carrara', 'woocommerce' ),
		'MT' => __( 'Matera', 'woocommerce' ),
		'ME' => __( 'Messina', 'woocommerce' ),
		'MI' => __( 'Milano', 'woocommerce' ),
		'MO' => __( 'Modena', 'woocommerce' ),
		'MB' => __( 'Monza e della Brianza', 'woocommerce' ),
		'NA' => __( 'Napoli', 'woocommerce' ),
		'NO' => __( 'Novara', 'woocommerce' ),
		'NU' => __( 'Nuoro', 'woocommerce' ),
		'OR' => __( 'Oristano', 'woocommerce' ),
		'PD' => __( 'Padova', 'woocommerce' ),
		'PA' => __( 'Palermo', 'woocommerce' ),
		'PR' => __( 'Parma', 'woocommerce' ),
		'PV' => __( 'Pavia', 'woocommerce' ),
		'PG' => __( 'Perugia', 'woocommerce' ),
		'PU' => __( 'Pesaro e Urbino', 'woocommerce' ),
		'PE' => __( 'Pescara', 'woocommerce' ),
		'PC' => __( 'Piacenza', 'woocommerce' ),
		'PI' => __( 'Pisa', 'woocommerce' ),
		'PT' => __( 'Pistoia', 'woocommerce' ),
		'PN' => __( 'Pordenone', 'woocommerce' ),
		'PZ' => __( 'Potenza', 'woocommerce' ),
		'PO' => __( 'Prato', 'woocommerce' ),
		'RG' => __( 'Ragusa', 'woocommerce' ),
		'RA' => __( 'Ravenna', 'woocommerce' ),
		'RC' => __( 'Reggio Calabria', 'woocommerce' ),
		'RE' => __( 'Reggio Emilia', 'woocommerce' ),
		'RI' => __( 'Rieti', 'woocommerce' ),
		'RN' => __( 'Rimini', 'woocommerce' ),
		'RM' => __( 'Roma', 'woocommerce' ),
		'RO' => __( 'Rovigo', 'woocommerce' ),
		'SA' => __( 'Salerno', 'woocommerce' ),
		'SS' => __( 'Sassari', 'woocommerce' ),
		'SV' => __( 'Savona', 'woocommerce' ),
		'SI' => __( 'Siena', 'woocommerce' ),
		'SR' => __( 'Siracusa', 'woocommerce' ),
		'SO' => __( 'Sondrio', 'woocommerce' ),
		'SU' => __( 'Sud Sardegna', 'woocommerce' ),
		'TA' => __( 'Taranto', 'woocommerce' ),
		'TE' => __( 'Teramo', 'woocommerce' ),
		'TR' => __( 'Terni', 'woocommerce' ),
		'TO' => __( 'Torino', 'woocommerce' ),
		'TP' => __( 'Trapani', 'woocommerce' ),
		'TN' => __( 'Trento', 'woocommerce' ),
		'TV' => __( 'Treviso', 'woocommerce' ),
		'TS' => __( 'Trieste', 'woocommerce' ),
		'UD' => __( 'Udine', 'woocommerce' ),
		'VA' => __( 'Varese', 'woocommerce' ),
		'VE' => __( 'Venezia', 'woocommerce' ),
		'VB' => __( 'Verbano-Cusio-Ossola', 'woocommerce' ),
		'VC' => __( 'Vercelli', 'woocommerce' ),
		'VR' => __( 'Verona', 'woocommerce' ),
		'VV' => __( 'Vibo Valentia', 'woocommerce' ),
		'VI' => __( 'Vicenza', 'woocommerce' ),
		'VT' => __( 'Viterbo', 'woocommerce' ),
	),
	'IL' => array(),
	'IM' => array(),

	/**
	 * Japan States.
	 *
	 * English notation of prefectures conform to the notation of Japan Post.
	 * The suffix corresponds with the Japanese translation file.
	 */
	'JP' => array(
		'JP01' => __( 'Hokkaido', 'woocommerce' ),
		'JP02' => __( 'Aomori', 'woocommerce' ),
		'JP03' => __( 'Iwate', 'woocommerce' ),
		'JP04' => __( 'Miyagi', 'woocommerce' ),
		'JP05' => __( 'Akita', 'woocommerce' ),
		'JP06' => __( 'Yamagata', 'woocommerce' ),
		'JP07' => __( 'Fukushima', 'woocommerce' ),
		'JP08' => __( 'Ibaraki', 'woocommerce' ),
		'JP09' => __( 'Tochigi', 'woocommerce' ),
		'JP10' => __( 'Gunma', 'woocommerce' ),
		'JP11' => __( 'Saitama', 'woocommerce' ),
		'JP12' => __( 'Chiba', 'woocommerce' ),
		'JP13' => __( 'Tokyo', 'woocommerce' ),
		'JP14' => __( 'Kanagawa', 'woocommerce' ),
		'JP15' => __( 'Niigata', 'woocommerce' ),
		'JP16' => __( 'Toyama', 'woocommerce' ),
		'JP17' => __( 'Ishikawa', 'woocommerce' ),
		'JP18' => __( 'Fukui', 'woocommerce' ),
		'JP19' => __( 'Yamanashi', 'woocommerce' ),
		'JP20' => __( 'Nagano', 'woocommerce' ),
		'JP21' => __( 'Gifu', 'woocommerce' ),
		'JP22' => __( 'Shizuoka', 'woocommerce' ),
		'JP23' => __( 'Aichi', 'woocommerce' ),
		'JP24' => __( 'Mie', 'woocommerce' ),
		'JP25' => __( 'Shiga', 'woocommerce' ),
		'JP26' => __( 'Kyoto', 'woocommerce' ),
		'JP27' => __( 'Osaka', 'woocommerce' ),
		'JP28' => __( 'Hyogo', 'woocommerce' ),
		'JP29' => __( 'Nara', 'woocommerce' ),
		'JP30' => __( 'Wakayama', 'woocommerce' ),
		'JP31' => __( 'Tottori', 'woocommerce' ),
		'JP32' => __( 'Shimane', 'woocommerce' ),
		'JP33' => __( 'Okayama', 'woocommerce' ),
		'JP34' => __( 'Hiroshima', 'woocommerce' ),
		'JP35' => __( 'Yamaguchi', 'woocommerce' ),
		'JP36' => __( 'Tokushima', 'woocommerce' ),
		'JP37' => __( 'Kagawa', 'woocommerce' ),
		'JP38' => __( 'Ehime', 'woocommerce' ),
		'JP39' => __( 'Kochi', 'woocommerce' ),
		'JP40' => __( 'Fukuoka', 'woocommerce' ),
		'JP41' => __( 'Saga', 'woocommerce' ),
		'JP42' => __( 'Nagasaki', 'woocommerce' ),
		'JP43' => __( 'Kumamoto', 'woocommerce' ),
		'JP44' => __( 'Oita', 'woocommerce' ),
		'JP45' => __( 'Miyazaki', 'woocommerce' ),
		'JP46' => __( 'Kagoshima', 'woocommerce' ),
		'JP47' => __( 'Okinawa', 'woocommerce' ),
	),
	'KR' => array(),
	'KW' => array(),
	'LB' => array(),
	'LR' => array( // Liberia provinces.
		'BM' => __( 'Bomi', 'woocommerce' ),
		'BN' => __( 'Bong', 'woocommerce' ),
		'GA' => __( 'Gbarpolu', 'woocommerce' ),
		'GB' => __( 'Grand Bassa', 'woocommerce' ),
		'GC' => __( 'Grand Cape Mount', 'woocommerce' ),
		'GG' => __( 'Grand Gedeh', 'woocommerce' ),
		'GK' => __( 'Grand Kru', 'woocommerce' ),
		'LO' => __( 'Lofa', 'woocommerce' ),
		'MA' => __( 'Margibi', 'woocommerce' ),
		'MY' => __( 'Maryland', 'woocommerce' ),
		'MO' => __( 'Montserrado', 'woocommerce' ),
		'NM' => __( 'Nimba', 'woocommerce' ),
		'RV' => __( 'Rivercess', 'woocommerce' ),
		'RG' => __( 'River Gee', 'woocommerce' ),
		'SN' => __( 'Sinoe', 'woocommerce' ),
	),
	'LU' => array(),
	'MD' => array( // Moldova states.
		'C'  => __( 'Chi&#537;in&#259;u', 'woocommerce' ),
		'BL' => __( 'B&#259;l&#539;i', 'woocommerce' ),
		'AN' => __( 'Anenii Noi', 'woocommerce' ),
		'BS' => __( 'Basarabeasca', 'woocommerce' ),
		'BR' => __( 'Briceni', 'woocommerce' ),
		'CH' => __( 'Cahul', 'woocommerce' ),
		'CT' => __( 'Cantemir', 'woocommerce' ),
		'CL' => __( 'C&#259;l&#259;ra&#537;i', 'woocommerce' ),
		'CS' => __( 'C&#259;u&#537;eni', 'woocommerce' ),
		'CM' => __( 'Cimi&#537;lia', 'woocommerce' ),
		'CR' => __( 'Criuleni', 'woocommerce' ),
		'DN' => __( 'Dondu&#537;eni', 'woocommerce' ),
		'DR' => __( 'Drochia', 'woocommerce' ),
		'DB' => __( 'Dub&#259;sari', 'woocommerce' ),
		'ED' => __( 'Edine&#539;', 'woocommerce' ),
		'FL' => __( 'F&#259;le&#537;ti', 'woocommerce' ),
		'FR' => __( 'Flore&#537;ti', 'woocommerce' ),
		'GE' => __( 'UTA G&#259;g&#259;uzia', 'woocommerce' ),
		'GL' => __( 'Glodeni', 'woocommerce' ),
		'HN' => __( 'H&icirc;nce&#537;ti', 'woocommerce' ),
		'IL' => __( 'Ialoveni', 'woocommerce' ),
		'LV' => __( 'Leova', 'woocommerce' ),
		'NS' => __( 'Nisporeni', 'woocommerce' ),
		'OC' => __( 'Ocni&#539;a', 'woocommerce' ),
		'OR' => __( 'Orhei', 'woocommerce' ),
		'RZ' => __( 'Rezina', 'woocommerce' ),
		'RS' => __( 'R&icirc;&#537;cani', 'woocommerce' ),
		'SG' => __( 'S&icirc;ngerei', 'woocommerce' ),
		'SR' => __( 'Soroca', 'woocommerce' ),
		'ST' => __( 'Str&#259;&#537;eni', 'woocommerce' ),
		'SD' => __( '&#536;old&#259;ne&#537;ti', 'woocommerce' ),
		'SV' => __( '&#536;tefan Vod&#259;', 'woocommerce' ),
		'TR' => __( 'Taraclia', 'woocommerce' ),
		'TL' => __( 'Telene&#537;ti', 'woocommerce' ),
		'UN' => __( 'Ungheni', 'woocommerce' ),
	),
	'MQ' => array(),
	'MT' => array(),
	'MX' => array( // Mexico States.
		'DF' => __( 'Ciudad de M&eacute;xico', 'woocommerce' ),
		'JA' => __( 'Jalisco', 'woocommerce' ),
		'NL' => __( 'Nuevo Le&oacute;n', 'woocommerce' ),
		'AG' => __( 'Aguascalientes', 'woocommerce' ),
		'BC' => __( 'Baja California', 'woocommerce' ),
		'BS' => __( 'Baja California Sur', 'woocommerce' ),
		'CM' => __( 'Campeche', 'woocommerce' ),
		'CS' => __( 'Chiapas', 'woocommerce' ),
		'CH' => __( 'Chihuahua', 'woocommerce' ),
		'CO' => __( 'Coahuila', 'woocommerce' ),
		'CL' => __( 'Colima', 'woocommerce' ),
		'DG' => __( 'Durango', 'woocommerce' ),
		'GT' => __( 'Guanajuato', 'woocommerce' ),
		'GR' => __( 'Guerrero', 'woocommerce' ),
		'HG' => __( 'Hidalgo', 'woocommerce' ),
		'MX' => __( 'Estado de M&eacute;xico', 'woocommerce' ),
		'MI' => __( 'Michoac&aacute;n', 'woocommerce' ),
		'MO' => __( 'Morelos', 'woocommerce' ),
		'NA' => __( 'Nayarit', 'woocommerce' ),
		'OA' => __( 'Oaxaca', 'woocommerce' ),
		'PU' => __( 'Puebla', 'woocommerce' ),
		'QT' => __( 'Quer&eacute;taro', 'woocommerce' ),
		'QR' => __( 'Quintana Roo', 'woocommerce' ),
		'SL' => __( 'San Luis Potos&iacute;', 'woocommerce' ),
		'SI' => __( 'Sinaloa', 'woocommerce' ),
		'SO' => __( 'Sonora', 'woocommerce' ),
		'TB' => __( 'Tabasco', 'woocommerce' ),
		'TM' => __( 'Tamaulipas', 'woocommerce' ),
		'TL' => __( 'Tlaxcala', 'woocommerce' ),
		'VE' => __( 'Veracruz', 'woocommerce' ),
		'YU' => __( 'Yucat&aacute;n', 'woocommerce' ),
		'ZA' => __( 'Zacatecas', 'woocommerce' ),
	),
	'MY' => array( // Malaysian states.
		'JHR' => __( 'Johor', 'woocommerce' ),
		'KDH' => __( 'Kedah', 'woocommerce' ),
		'KTN' => __( 'Kelantan', 'woocommerce' ),
		'LBN' => __( 'Labuan', 'woocommerce' ),
		'MLK' => __( 'Malacca (Melaka)', 'woocommerce' ),
		'NSN' => __( 'Negeri Sembilan', 'woocommerce' ),
		'PHG' => __( 'Pahang', 'woocommerce' ),
		'PNG' => __( 'Penang (Pulau Pinang)', 'woocommerce' ),
		'PRK' => __( 'Perak', 'woocommerce' ),
		'PLS' => __( 'Perlis', 'woocommerce' ),
		'SBH' => __( 'Sabah', 'woocommerce' ),
		'SWK' => __( 'Sarawak', 'woocommerce' ),
		'SGR' => __( 'Selangor', 'woocommerce' ),
		'TRG' => __( 'Terengganu', 'woocommerce' ),
		'PJY' => __( 'Putrajaya', 'woocommerce' ),
		'KUL' => __( 'Kuala Lumpur', 'woocommerce' ),
	),
	'NG' => array( // Nigerian provinces.
		'AB' => __( 'Abia', 'woocommerce' ),
		'FC' => __( 'Abuja', 'woocommerce' ),
		'AD' => __( 'Adamawa', 'woocommerce' ),
		'AK' => __( 'Akwa Ibom', 'woocommerce' ),
		'AN' => __( 'Anambra', 'woocommerce' ),
		'BA' => __( 'Bauchi', 'woocommerce' ),
		'BY' => __( 'Bayelsa', 'woocommerce' ),
		'BE' => __( 'Benue', 'woocommerce' ),
		'BO' => __( 'Borno', 'woocommerce' ),
		'CR' => __( 'Cross River', 'woocommerce' ),
		'DE' => __( 'Delta', 'woocommerce' ),
		'EB' => __( 'Ebonyi', 'woocommerce' ),
		'ED' => __( 'Edo', 'woocommerce' ),
		'EK' => __( 'Ekiti', 'woocommerce' ),
		'EN' => __( 'Enugu', 'woocommerce' ),
		'GO' => __( 'Gombe', 'woocommerce' ),
		'IM' => __( 'Imo', 'woocommerce' ),
		'JI' => __( 'Jigawa', 'woocommerce' ),
		'KD' => __( 'Kaduna', 'woocommerce' ),
		'KN' => __( 'Kano', 'woocommerce' ),
		'KT' => __( 'Katsina', 'woocommerce' ),
		'KE' => __( 'Kebbi', 'woocommerce' ),
		'KO' => __( 'Kogi', 'woocommerce' ),
		'KW' => __( 'Kwara', 'woocommerce' ),
		'LA' => __( 'Lagos', 'woocommerce' ),
		'NA' => __( 'Nasarawa', 'woocommerce' ),
		'NI' => __( 'Niger', 'woocommerce' ),
		'OG' => __( 'Ogun', 'woocommerce' ),
		'ON' => __( 'Ondo', 'woocommerce' ),
		'OS' => __( 'Osun', 'woocommerce' ),
		'OY' => __( 'Oyo', 'woocommerce' ),
		'PL' => __( 'Plateau', 'woocommerce' ),
		'RI' => __( 'Rivers', 'woocommerce' ),
		'SO' => __( 'Sokoto', 'woocommerce' ),
		'TA' => __( 'Taraba', 'woocommerce' ),
		'YO' => __( 'Yobe', 'woocommerce' ),
		'ZA' => __( 'Zamfara', 'woocommerce' ),
	),
	'NL' => array(),
	'NO' => array(),
	'NP' => array( // Nepal states (Zones).
		'BAG' => __( 'Bagmati', 'woocommerce' ),
		'BHE' => __( 'Bheri', 'woocommerce' ),
		'DHA' => __( 'Dhaulagiri', 'woocommerce' ),
		'GAN' => __( 'Gandaki', 'woocommerce' ),
		'JAN' => __( 'Janakpur', 'woocommerce' ),
		'KAR' => __( 'Karnali', 'woocommerce' ),
		'KOS' => __( 'Koshi', 'woocommerce' ),
		'LUM' => __( 'Lumbini', 'woocommerce' ),
		'MAH' => __( 'Mahakali', 'woocommerce' ),
		'MEC' => __( 'Mechi', 'woocommerce' ),
		'NAR' => __( 'Narayani', 'woocommerce' ),
		'RAP' => __( 'Rapti', 'woocommerce' ),
		'SAG' => __( 'Sagarmatha', 'woocommerce' ),
		'SET' => __( 'Seti', 'woocommerce' ),
	),
	'NZ' => array( // New Zealand States.
		'NL' => __( 'Northland', 'woocommerce' ),
		'AK' => __( 'Auckland', 'woocommerce' ),
		'WA' => __( 'Waikato', 'woocommerce' ),
		'BP' => __( 'Bay of Plenty', 'woocommerce' ),
		'TK' => __( 'Taranaki', 'woocommerce' ),
		'GI' => __( 'Gisborne', 'woocommerce' ),
		'HB' => __( 'Hawke&rsquo;s Bay', 'woocommerce' ),
		'MW' => __( 'Manawatu-Wanganui', 'woocommerce' ),
		'WE' => __( 'Wellington', 'woocommerce' ),
		'NS' => __( 'Nelson', 'woocommerce' ),
		'MB' => __( 'Marlborough', 'woocommerce' ),
		'TM' => __( 'Tasman', 'woocommerce' ),
		'WC' => __( 'West Coast', 'woocommerce' ),
		'CT' => __( 'Canterbury', 'woocommerce' ),
		'OT' => __( 'Otago', 'woocommerce' ),
		'SL' => __( 'Southland', 'woocommerce' ),
	),
	'PE' => array( // Peru states.
		'CAL' => __( 'El Callao', 'woocommerce' ),
		'LMA' => __( 'Municipalidad Metropolitana de Lima', 'woocommerce' ),
		'AMA' => __( 'Amazonas', 'woocommerce' ),
		'ANC' => __( 'Ancash', 'woocommerce' ),
		'APU' => __( 'Apur&iacute;mac', 'woocommerce' ),
		'ARE' => __( 'Arequipa', 'woocommerce' ),
		'AYA' => __( 'Ayacucho', 'woocommerce' ),
		'CAJ' => __( 'Cajamarca', 'woocommerce' ),
		'CUS' => __( 'Cusco', 'woocommerce' ),
		'HUV' => __( 'Huancavelica', 'woocommerce' ),
		'HUC' => __( 'Hu&aacute;nuco', 'woocommerce' ),
		'ICA' => __( 'Ica', 'woocommerce' ),
		'JUN' => __( 'Jun&iacute;n', 'woocommerce' ),
		'LAL' => __( 'La Libertad', 'woocommerce' ),
		'LAM' => __( 'Lambayeque', 'woocommerce' ),
		'LIM' => __( 'Lima', 'woocommerce' ),
		'LOR' => __( 'Loreto', 'woocommerce' ),
		'MDD' => __( 'Madre de Dios', 'woocommerce' ),
		'MOQ' => __( 'Moquegua', 'woocommerce' ),
		'PAS' => __( 'Pasco', 'woocommerce' ),
		'PIU' => __( 'Piura', 'woocommerce' ),
		'PUN' => __( 'Puno', 'woocommerce' ),
		'SAM' => __( 'San Mart&iacute;n', 'woocommerce' ),
		'TAC' => __( 'Tacna', 'woocommerce' ),
		'TUM' => __( 'Tumbes', 'woocommerce' ),
		'UCA' => __( 'Ucayali', 'woocommerce' ),
	),

	/**
	 * Philippine Provinces.
	 *
	 * @todo DAC Needs to be updated when ISO code is assigned.
	 */
	'PH' => array(
		'ABR' => __( 'Abra', 'woocommerce' ),
		'AGN' => __( 'Agusan del Norte', 'woocommerce' ),
		'AGS' => __( 'Agusan del Sur', 'woocommerce' ),
		'AKL' => __( 'Aklan', 'woocommerce' ),
		'ALB' => __( 'Albay', 'woocommerce' ),
		'ANT' => __( 'Antique', 'woocommerce' ),
		'APA' => __( 'Apayao', 'woocommerce' ),
		'AUR' => __( 'Aurora', 'woocommerce' ),
		'BAS' => __( 'Basilan', 'woocommerce' ),
		'BAN' => __( 'Bataan', 'woocommerce' ),
		'BTN' => __( 'Batanes', 'woocommerce' ),
		'BTG' => __( 'Batangas', 'woocommerce' ),
		'BEN' => __( 'Benguet', 'woocommerce' ),
		'BIL' => __( 'Biliran', 'woocommerce' ),
		'BOH' => __( 'Bohol', 'woocommerce' ),
		'BUK' => __( 'Bukidnon', 'woocommerce' ),
		'BUL' => __( 'Bulacan', 'woocommerce' ),
		'CAG' => __( 'Cagayan', 'woocommerce' ),
		'CAN' => __( 'Camarines Norte', 'woocommerce' ),
		'CAS' => __( 'Camarines Sur', 'woocommerce' ),
		'CAM' => __( 'Camiguin', 'woocommerce' ),
		'CAP' => __( 'Capiz', 'woocommerce' ),
		'CAT' => __( 'Catanduanes', 'woocommerce' ),
		'CAV' => __( 'Cavite', 'woocommerce' ),
		'CEB' => __( 'Cebu', 'woocommerce' ),
		'COM' => __( 'Compostela Valley', 'woocommerce' ),
		'NCO' => __( 'Cotabato', 'woocommerce' ),
		'DAV' => __( 'Davao del Norte', 'woocommerce' ),
		'DAS' => __( 'Davao del Sur', 'woocommerce' ),
		'DAC' => __( 'Davao Occidental', 'woocommerce' ),
		'DAO' => __( 'Davao Oriental', 'woocommerce' ),
		'DIN' => __( 'Dinagat Islands', 'woocommerce' ),
		'EAS' => __( 'Eastern Samar', 'woocommerce' ),
		'GUI' => __( 'Guimaras', 'woocommerce' ),
		'IFU' => __( 'Ifugao', 'woocommerce' ),
		'ILN' => __( 'Ilocos Norte', 'woocommerce' ),
		'ILS' => __( 'Ilocos Sur', 'woocommerce' ),
		'ILI' => __( 'Iloilo', 'woocommerce' ),
		'ISA' => __( 'Isabela', 'woocommerce' ),
		'KAL' => __( 'Kalinga', 'woocommerce' ),
		'LUN' => __( 'La Union', 'woocommerce' ),
		'LAG' => __( 'Laguna', 'woocommerce' ),
		'LAN' => __( 'Lanao del Norte', 'woocommerce' ),
		'LAS' => __( 'Lanao del Sur', 'woocommerce' ),
		'LEY' => __( 'Leyte', 'woocommerce' ),
		'MAG' => __( 'Maguindanao', 'woocommerce' ),
		'MAD' => __( 'Marinduque', 'woocommerce' ),
		'MAS' => __( 'Masbate', 'woocommerce' ),
		'MSC' => __( 'Misamis Occidental', 'woocommerce' ),
		'MSR' => __( 'Misamis Oriental', 'woocommerce' ),
		'MOU' => __( 'Mountain Province', 'woocommerce' ),
		'NEC' => __( 'Negros Occidental', 'woocommerce' ),
		'NER' => __( 'Negros Oriental', 'woocommerce' ),
		'NSA' => __( 'Northern Samar', 'woocommerce' ),
		'NUE' => __( 'Nueva Ecija', 'woocommerce' ),
		'NUV' => __( 'Nueva Vizcaya', 'woocommerce' ),
		'MDC' => __( 'Occidental Mindoro', 'woocommerce' ),
		'MDR' => __( 'Oriental Mindoro', 'woocommerce' ),
		'PLW' => __( 'Palawan', 'woocommerce' ),
		'PAM' => __( 'Pampanga', 'woocommerce' ),
		'PAN' => __( 'Pangasinan', 'woocommerce' ),
		'QUE' => __( 'Quezon', 'woocommerce' ),
		'QUI' => __( 'Quirino', 'woocommerce' ),
		'RIZ' => __( 'Rizal', 'woocommerce' ),
		'ROM' => __( 'Romblon', 'woocommerce' ),
		'WSA' => __( 'Samar', 'woocommerce' ),
		'SAR' => __( 'Sarangani', 'woocommerce' ),
		'SIQ' => __( 'Siquijor', 'woocommerce' ),
		'SOR' => __( 'Sorsogon', 'woocommerce' ),
		'SCO' => __( 'South Cotabato', 'woocommerce' ),
		'SLE' => __( 'Southern Leyte', 'woocommerce' ),
		'SUK' => __( 'Sultan Kudarat', 'woocommerce' ),
		'SLU' => __( 'Sulu', 'woocommerce' ),
		'SUN' => __( 'Surigao del Norte', 'woocommerce' ),
		'SUR' => __( 'Surigao del Sur', 'woocommerce' ),
		'TAR' => __( 'Tarlac', 'woocommerce' ),
		'TAW' => __( 'Tawi-Tawi', 'woocommerce' ),
		'ZMB' => __( 'Zambales', 'woocommerce' ),
		'ZAN' => __( 'Zamboanga del Norte', 'woocommerce' ),
		'ZAS' => __( 'Zamboanga del Sur', 'woocommerce' ),
		'ZSI' => __( 'Zamboanga Sibugay', 'woocommerce' ),
		'00'  => __( 'Metro Manila', 'woocommerce' ),
	),
	'PK' => array( // Pakistan's states.
		'JK' => __( 'Azad Kashmir', 'woocommerce' ),
		'BA' => __( 'Balochistan', 'woocommerce' ),
		'TA' => __( 'FATA', 'woocommerce' ),
		'GB' => __( 'Gilgit Baltistan', 'woocommerce' ),
		'IS' => __( 'Islamabad Capital Territory', 'woocommerce' ),
		'KP' => __( 'Khyber Pakhtunkhwa', 'woocommerce' ),
		'PB' => __( 'Punjab', 'woocommerce' ),
		'SD' => __( 'Sindh', 'woocommerce' ),
	),
	'PL' => array(),
	'PT' => array(),
	'PY' => array( // Paraguay states.
		'PY-ASU' => __( 'Asunci&oacute;n', 'woocommerce' ),
		'PY-1'   => __( 'Concepci&oacute;n', 'woocommerce' ),
		'PY-2'   => __( 'San Pedro', 'woocommerce' ),
		'PY-3'   => __( 'Cordillera', 'woocommerce' ),
		'PY-4'   => __( 'Guair&aacute;', 'woocommerce' ),
		'PY-5'   => __( 'Caaguaz&uacute;', 'woocommerce' ),
		'PY-6'   => __( 'Caazap&aacute;', 'woocommerce' ),
		'PY-7'   => __( 'Itap&uacute;a', 'woocommerce' ),
		'PY-8'   => __( 'Misiones', 'woocommerce' ),
		'PY-9'   => __( 'Paraguar&iacute;', 'woocommerce' ),
		'PY-10'  => __( 'Alto Paran&aacute;', 'woocommerce' ),
		'PY-11'  => __( 'Central', 'woocommerce' ),
		'PY-12'  => __( '&Ntilde;eembuc&uacute;', 'woocommerce' ),
		'PY-13'  => __( 'Amambay', 'woocommerce' ),
		'PY-14'  => __( 'Canindey&uacute;', 'woocommerce' ),
		'PY-15'  => __( 'Presidente Hayes', 'woocommerce' ),
		'PY-16'  => __( 'Alto Paraguay', 'woocommerce' ),
		'PY-17'  => __( 'Boquer&oacute;n', 'woocommerce' ),
	),
	'RE' => array(),
	'RO' => array( // Romania states.
		'AB' => __( 'Alba', 'woocommerce' ),
		'AR' => __( 'Arad', 'woocommerce' ),
		'AG' => __( 'Arge&#537;', 'woocommerce' ),
		'BC' => __( 'Bac&#259;u', 'woocommerce' ),
		'BH' => __( 'Bihor', 'woocommerce' ),
		'BN' => __( 'Bistri&#539;a-N&#259;s&#259;ud', 'woocommerce' ),
		'BT' => __( 'Boto&#537;ani', 'woocommerce' ),
		'BR' => __( 'Br&#259;ila', 'woocommerce' ),
		'BV' => __( 'Bra&#537;ov', 'woocommerce' ),
		'B'  => __( 'Bucure&#537;ti', 'woocommerce' ),
		'BZ' => __( 'Buz&#259;u', 'woocommerce' ),
		'CL' => __( 'C&#259;l&#259;ra&#537;i', 'woocommerce' ),
		'CS' => __( 'Cara&#537;-Severin', 'woocommerce' ),
		'CJ' => __( 'Cluj', 'woocommerce' ),
		'CT' => __( 'Constan&#539;a', 'woocommerce' ),
		'CV' => __( 'Covasna', 'woocommerce' ),
		'DB' => __( 'D&acirc;mbovi&#539;a', 'woocommerce' ),
		'DJ' => __( 'Dolj', 'woocommerce' ),
		'GL' => __( 'Gala&#539;i', 'woocommerce' ),
		'GR' => __( 'Giurgiu', 'woocommerce' ),
		'GJ' => __( 'Gorj', 'woocommerce' ),
		'HR' => __( 'Harghita', 'woocommerce' ),
		'HD' => __( 'Hunedoara', 'woocommerce' ),
		'IL' => __( 'Ialomi&#539;a', 'woocommerce' ),
		'IS' => __( 'Ia&#537;i', 'woocommerce' ),
		'IF' => __( 'Ilfov', 'woocommerce' ),
		'MM' => __( 'Maramure&#537;', 'woocommerce' ),
		'MH' => __( 'Mehedin&#539;i', 'woocommerce' ),
		'MS' => __( 'Mure&#537;', 'woocommerce' ),
		'NT' => __( 'Neam&#539;', 'woocommerce' ),
		'OT' => __( 'Olt', 'woocommerce' ),
		'PH' => __( 'Prahova', 'woocommerce' ),
		'SJ' => __( 'S&#259;laj', 'woocommerce' ),
		'SM' => __( 'Satu Mare', 'woocommerce' ),
		'SB' => __( 'Sibiu', 'woocommerce' ),
		'SV' => __( 'Suceava', 'woocommerce' ),
		'TR' => __( 'Teleorman', 'woocommerce' ),
		'TM' => __( 'Timi&#537;', 'woocommerce' ),
		'TL' => __( 'Tulcea', 'woocommerce' ),
		'VL' => __( 'V&acirc;lcea', 'woocommerce' ),
		'VS' => __( 'Vaslui', 'woocommerce' ),
		'VN' => __( 'Vrancea', 'woocommerce' ),
	),
	'RS' => array(),
	'SG' => array(),
	'SK' => array(),
	'SI' => array(),
	'TH' => array( // Thailand states.
		'TH-37' => __( 'Amnat Charoen', 'woocommerce' ),
		'TH-15' => __( 'Ang Thong', 'woocommerce' ),
		'TH-14' => __( 'Ayutthaya', 'woocommerce' ),
		'TH-10' => __( 'Bangkok', 'woocommerce' ),
		'TH-38' => __( 'Bueng Kan', 'woocommerce' ),
		'TH-31' => __( 'Buri Ram', 'woocommerce' ),
		'TH-24' => __( 'Chachoengsao', 'woocommerce' ),
		'TH-18' => __( 'Chai Nat', 'woocommerce' ),
		'TH-36' => __( 'Chaiyaphum', 'woocommerce' ),
		'TH-22' => __( 'Chanthaburi', 'woocommerce' ),
		'TH-50' => __( 'Chiang Mai', 'woocommerce' ),
		'TH-57' => __( 'Chiang Rai', 'woocommerce' ),
		'TH-20' => __( 'Chonburi', 'woocommerce' ),
		'TH-86' => __( 'Chumphon', 'woocommerce' ),
		'TH-46' => __( 'Kalasin', 'woocommerce' ),
		'TH-62' => __( 'Kamphaeng Phet', 'woocommerce' ),
		'TH-71' => __( 'Kanchanaburi', 'woocommerce' ),
		'TH-40' => __( 'Khon Kaen', 'woocommerce' ),
		'TH-81' => __( 'Krabi', 'woocommerce' ),
		'TH-52' => __( 'Lampang', 'woocommerce' ),
		'TH-51' => __( 'Lamphun', 'woocommerce' ),
		'TH-42' => __( 'Loei', 'woocommerce' ),
		'TH-16' => __( 'Lopburi', 'woocommerce' ),
		'TH-58' => __( 'Mae Hong Son', 'woocommerce' ),
		'TH-44' => __( 'Maha Sarakham', 'woocommerce' ),
		'TH-49' => __( 'Mukdahan', 'woocommerce' ),
		'TH-26' => __( 'Nakhon Nayok', 'woocommerce' ),
		'TH-73' => __( 'Nakhon Pathom', 'woocommerce' ),
		'TH-48' => __( 'Nakhon Phanom', 'woocommerce' ),
		'TH-30' => __( 'Nakhon Ratchasima', 'woocommerce' ),
		'TH-60' => __( 'Nakhon Sawan', 'woocommerce' ),
		'TH-80' => __( 'Nakhon Si Thammarat', 'woocommerce' ),
		'TH-55' => __( 'Nan', 'woocommerce' ),
		'TH-96' => __( 'Narathiwat', 'woocommerce' ),
		'TH-39' => __( 'Nong Bua Lam Phu', 'woocommerce' ),
		'TH-43' => __( 'Nong Khai', 'woocommerce' ),
		'TH-12' => __( 'Nonthaburi', 'woocommerce' ),
		'TH-13' => __( 'Pathum Thani', 'woocommerce' ),
		'TH-94' => __( 'Pattani', 'woocommerce' ),
		'TH-82' => __( 'Phang Nga', 'woocommerce' ),
		'TH-93' => __( 'Phatthalung', 'woocommerce' ),
		'TH-56' => __( 'Phayao', 'woocommerce' ),
		'TH-67' => __( 'Phetchabun', 'woocommerce' ),
		'TH-76' => __( 'Phetchaburi', 'woocommerce' ),
		'TH-66' => __( 'Phichit', 'woocommerce' ),
		'TH-65' => __( 'Phitsanulok', 'woocommerce' ),
		'TH-54' => __( 'Phrae', 'woocommerce' ),
		'TH-83' => __( 'Phuket', 'woocommerce' ),
		'TH-25' => __( 'Prachin Buri', 'woocommerce' ),
		'TH-77' => __( 'Prachuap Khiri Khan', 'woocommerce' ),
		'TH-85' => __( 'Ranong', 'woocommerce' ),
		'TH-70' => __( 'Ratchaburi', 'woocommerce' ),
		'TH-21' => __( 'Rayong', 'woocommerce' ),
		'TH-45' => __( 'Roi Et', 'woocommerce' ),
		'TH-27' => __( 'Sa Kaeo', 'woocommerce' ),
		'TH-47' => __( 'Sakon Nakhon', 'woocommerce' ),
		'TH-11' => __( 'Samut Prakan', 'woocommerce' ),
		'TH-74' => __( 'Samut Sakhon', 'woocommerce' ),
		'TH-75' => __( 'Samut Songkhram', 'woocommerce' ),
		'TH-19' => __( 'Saraburi', 'woocommerce' ),
		'TH-91' => __( 'Satun', 'woocommerce' ),
		'TH-17' => __( 'Sing Buri', 'woocommerce' ),
		'TH-33' => __( 'Sisaket', 'woocommerce' ),
		'TH-90' => __( 'Songkhla', 'woocommerce' ),
		'TH-64' => __( 'Sukhothai', 'woocommerce' ),
		'TH-72' => __( 'Suphan Buri', 'woocommerce' ),
		'TH-84' => __( 'Surat Thani', 'woocommerce' ),
		'TH-32' => __( 'Surin', 'woocommerce' ),
		'TH-63' => __( 'Tak', 'woocommerce' ),
		'TH-92' => __( 'Trang', 'woocommerce' ),
		'TH-23' => __( 'Trat', 'woocommerce' ),
		'TH-34' => __( 'Ubon Ratchathani', 'woocommerce' ),
		'TH-41' => __( 'Udon Thani', 'woocommerce' ),
		'TH-61' => __( 'Uthai Thani', 'woocommerce' ),
		'TH-53' => __( 'Uttaradit', 'woocommerce' ),
		'TH-95' => __( 'Yala', 'woocommerce' ),
		'TH-35' => __( 'Yasothon', 'woocommerce' ),
	),
	'TR' => array( // Turkey States.
		'TR01' => __( 'Adana', 'woocommerce' ),
		'TR02' => __( 'Ad&#305;yaman', 'woocommerce' ),
		'TR03' => __( 'Afyon', 'woocommerce' ),
		'TR04' => __( 'A&#287;r&#305;', 'woocommerce' ),
		'TR05' => __( 'Amasya', 'woocommerce' ),
		'TR06' => __( 'Ankara', 'woocommerce' ),
		'TR07' => __( 'Antalya', 'woocommerce' ),
		'TR08' => __( 'Artvin', 'woocommerce' ),
		'TR09' => __( 'Ayd&#305;n', 'woocommerce' ),
		'TR10' => __( 'Bal&#305;kesir', 'woocommerce' ),
		'TR11' => __( 'Bilecik', 'woocommerce' ),
		'TR12' => __( 'Bing&#246;l', 'woocommerce' ),
		'TR13' => __( 'Bitlis', 'woocommerce' ),
		'TR14' => __( 'Bolu', 'woocommerce' ),
		'TR15' => __( 'Burdur', 'woocommerce' ),
		'TR16' => __( 'Bursa', 'woocommerce' ),
		'TR17' => __( '&#199;anakkale', 'woocommerce' ),
		'TR18' => __( '&#199;ank&#305;r&#305;', 'woocommerce' ),
		'TR19' => __( '&#199;orum', 'woocommerce' ),
		'TR20' => __( 'Denizli', 'woocommerce' ),
		'TR21' => __( 'Diyarbak&#305;r', 'woocommerce' ),
		'TR22' => __( 'Edirne', 'woocommerce' ),
		'TR23' => __( 'Elaz&#305;&#287;', 'woocommerce' ),
		'TR24' => __( 'Erzincan', 'woocommerce' ),
		'TR25' => __( 'Erzurum', 'woocommerce' ),
		'TR26' => __( 'Eski&#351;ehir', 'woocommerce' ),
		'TR27' => __( 'Gaziantep', 'woocommerce' ),
		'TR28' => __( 'Giresun', 'woocommerce' ),
		'TR29' => __( 'G&#252;m&#252;&#351;hane', 'woocommerce' ),
		'TR30' => __( 'Hakkari', 'woocommerce' ),
		'TR31' => __( 'Hatay', 'woocommerce' ),
		'TR32' => __( 'Isparta', 'woocommerce' ),
		'TR33' => __( '&#304;&#231;el', 'woocommerce' ),
		'TR34' => __( '&#304;stanbul', 'woocommerce' ),
		'TR35' => __( '&#304;zmir', 'woocommerce' ),
		'TR36' => __( 'Kars', 'woocommerce' ),
		'TR37' => __( 'Kastamonu', 'woocommerce' ),
		'TR38' => __( 'Kayseri', 'woocommerce' ),
		'TR39' => __( 'K&#305;rklareli', 'woocommerce' ),
		'TR40' => __( 'K&#305;r&#351;ehir', 'woocommerce' ),
		'TR41' => __( 'Kocaeli', 'woocommerce' ),
		'TR42' => __( 'Konya', 'woocommerce' ),
		'TR43' => __( 'K&#252;tahya', 'woocommerce' ),
		'TR44' => __( 'Malatya', 'woocommerce' ),
		'TR45' => __( 'Manisa', 'woocommerce' ),
		'TR46' => __( 'Kahramanmara&#351;', 'woocommerce' ),
		'TR47' => __( 'Mardin', 'woocommerce' ),
		'TR48' => __( 'Mu&#287;la', 'woocommerce' ),
		'TR49' => __( 'Mu&#351;', 'woocommerce' ),
		'TR50' => __( 'Nev&#351;ehir', 'woocommerce' ),
		'TR51' => __( 'Ni&#287;de', 'woocommerce' ),
		'TR52' => __( 'Ordu', 'woocommerce' ),
		'TR53' => __( 'Rize', 'woocommerce' ),
		'TR54' => __( 'Sakarya', 'woocommerce' ),
		'TR55' => __( 'Samsun', 'woocommerce' ),
		'TR56' => __( 'Siirt', 'woocommerce' ),
		'TR57' => __( 'Sinop', 'woocommerce' ),
		'TR58' => __( 'Sivas', 'woocommerce' ),
		'TR59' => __( 'Tekirda&#287;', 'woocommerce' ),
		'TR60' => __( 'Tokat', 'woocommerce' ),
		'TR61' => __( 'Trabzon', 'woocommerce' ),
		'TR62' => __( 'Tunceli', 'woocommerce' ),
		'TR63' => __( '&#350;anl&#305;urfa', 'woocommerce' ),
		'TR64' => __( 'U&#351;ak', 'woocommerce' ),
		'TR65' => __( 'Van', 'woocommerce' ),
		'TR66' => __( 'Yozgat', 'woocommerce' ),
		'TR67' => __( 'Zonguldak', 'woocommerce' ),
		'TR68' => __( 'Aksaray', 'woocommerce' ),
		'TR69' => __( 'Bayburt', 'woocommerce' ),
		'TR70' => __( 'Karaman', 'woocommerce' ),
		'TR71' => __( 'K&#305;r&#305;kkale', 'woocommerce' ),
		'TR72' => __( 'Batman', 'woocommerce' ),
		'TR73' => __( '&#350;&#305;rnak', 'woocommerce' ),
		'TR74' => __( 'Bart&#305;n', 'woocommerce' ),
		'TR75' => __( 'Ardahan', 'woocommerce' ),
		'TR76' => __( 'I&#287;d&#305;r', 'woocommerce' ),
		'TR77' => __( 'Yalova', 'woocommerce' ),
		'TR78' => __( 'Karab&#252;k', 'woocommerce' ),
		'TR79' => __( 'Kilis', 'woocommerce' ),
		'TR80' => __( 'Osmaniye', 'woocommerce' ),
		'TR81' => __( 'D&#252;zce', 'woocommerce' ),
	),
	'TZ' => array( // Tanzania States.
		'TZ01' => __( 'Arusha', 'woocommerce' ),
		'TZ02' => __( 'Dar es Salaam', 'woocommerce' ),
		'TZ03' => __( 'Dodoma', 'woocommerce' ),
		'TZ04' => __( 'Iringa', 'woocommerce' ),
		'TZ05' => __( 'Kagera', 'woocommerce' ),
		'TZ06' => __( 'Pemba North', 'woocommerce' ),
		'TZ07' => __( 'Zanzibar North', 'woocommerce' ),
		'TZ08' => __( 'Kigoma', 'woocommerce' ),
		'TZ09' => __( 'Kilimanjaro', 'woocommerce' ),
		'TZ10' => __( 'Pemba South', 'woocommerce' ),
		'TZ11' => __( 'Zanzibar South', 'woocommerce' ),
		'TZ12' => __( 'Lindi', 'woocommerce' ),
		'TZ13' => __( 'Mara', 'woocommerce' ),
		'TZ14' => __( 'Mbeya', 'woocommerce' ),
		'TZ15' => __( 'Zanzibar West', 'woocommerce' ),
		'TZ16' => __( 'Morogoro', 'woocommerce' ),
		'TZ17' => __( 'Mtwara', 'woocommerce' ),
		'TZ18' => __( 'Mwanza', 'woocommerce' ),
		'TZ19' => __( 'Coast', 'woocommerce' ),
		'TZ20' => __( 'Rukwa', 'woocommerce' ),
		'TZ21' => __( 'Ruvuma', 'woocommerce' ),
		'TZ22' => __( 'Shinyanga', 'woocommerce' ),
		'TZ23' => __( 'Singida', 'woocommerce' ),
		'TZ24' => __( 'Tabora', 'woocommerce' ),
		'TZ25' => __( 'Tanga', 'woocommerce' ),
		'TZ26' => __( 'Manyara', 'woocommerce' ),
		'TZ27' => __( 'Geita', 'woocommerce' ),
		'TZ28' => __( 'Katavi', 'woocommerce' ),
		'TZ29' => __( 'Njombe', 'woocommerce' ),
		'TZ30' => __( 'Simiyu', 'woocommerce' ),
	),
	'LK' => array(),
	'SE' => array(),
	'US' => array( // United States.
		'AL' => __( 'Alabama', 'woocommerce' ),
		'AK' => __( 'Alaska', 'woocommerce' ),
		'AZ' => __( 'Arizona', 'woocommerce' ),
		'AR' => __( 'Arkansas', 'woocommerce' ),
		'CA' => __( 'California', 'woocommerce' ),
		'CO' => __( 'Colorado', 'woocommerce' ),
		'CT' => __( 'Connecticut', 'woocommerce' ),
		'DE' => __( 'Delaware', 'woocommerce' ),
		'DC' => __( 'District Of Columbia', 'woocommerce' ),
		'FL' => __( 'Florida', 'woocommerce' ),
		'GA' => _x( 'Georgia', 'US state of Georgia', 'woocommerce' ),
		'HI' => __( 'Hawaii', 'woocommerce' ),
		'ID' => __( 'Idaho', 'woocommerce' ),
		'IL' => __( 'Illinois', 'woocommerce' ),
		'IN' => __( 'Indiana', 'woocommerce' ),
		'IA' => __( 'Iowa', 'woocommerce' ),
		'KS' => __( 'Kansas', 'woocommerce' ),
		'KY' => __( 'Kentucky', 'woocommerce' ),
		'LA' => __( 'Louisiana', 'woocommerce' ),
		'ME' => __( 'Maine', 'woocommerce' ),
		'MD' => __( 'Maryland', 'woocommerce' ),
		'MA' => __( 'Massachusetts', 'woocommerce' ),
		'MI' => __( 'Michigan', 'woocommerce' ),
		'MN' => __( 'Minnesota', 'woocommerce' ),
		'MS' => __( 'Mississippi', 'woocommerce' ),
		'MO' => __( 'Missouri', 'woocommerce' ),
		'MT' => __( 'Montana', 'woocommerce' ),
		'NE' => __( 'Nebraska', 'woocommerce' ),
		'NV' => __( 'Nevada', 'woocommerce' ),
		'NH' => __( 'New Hampshire', 'woocommerce' ),
		'NJ' => __( 'New Jersey', 'woocommerce' ),
		'NM' => __( 'New Mexico', 'woocommerce' ),
		'NY' => __( 'New York', 'woocommerce' ),
		'NC' => __( 'North Carolina', 'woocommerce' ),
		'ND' => __( 'North Dakota', 'woocommerce' ),
		'OH' => __( 'Ohio', 'woocommerce' ),
		'OK' => __( 'Oklahoma', 'woocommerce' ),
		'OR' => __( 'Oregon', 'woocommerce' ),
		'PA' => __( 'Pennsylvania', 'woocommerce' ),
		'RI' => __( 'Rhode Island', 'woocommerce' ),
		'SC' => __( 'South Carolina', 'woocommerce' ),
		'SD' => __( 'South Dakota', 'woocommerce' ),
		'TN' => __( 'Tennessee', 'woocommerce' ),
		'TX' => __( 'Texas', 'woocommerce' ),
		'UT' => __( 'Utah', 'woocommerce' ),
		'VT' => __( 'Vermont', 'woocommerce' ),
		'VA' => __( 'Virginia', 'woocommerce' ),
		'WA' => __( 'Washington', 'woocommerce' ),
		'WV' => __( 'West Virginia', 'woocommerce' ),
		'WI' => __( 'Wisconsin', 'woocommerce' ),
		'WY' => __( 'Wyoming', 'woocommerce' ),
		'AA' => __( 'Armed Forces (AA)', 'woocommerce' ),
		'AE' => __( 'Armed Forces (AE)', 'woocommerce' ),
		'AP' => __( 'Armed Forces (AP)', 'woocommerce' ),
	),
	'VN' => array(),
	'YT' => array(),
	'ZA' => array( // South African states.
		'EC'  => __( 'Eastern Cape', 'woocommerce' ),
		'FS'  => __( 'Free State', 'woocommerce' ),
		'GP'  => __( 'Gauteng', 'woocommerce' ),
		'KZN' => __( 'KwaZulu-Natal', 'woocommerce' ),
		'LP'  => __( 'Limpopo', 'woocommerce' ),
		'MP'  => __( 'Mpumalanga', 'woocommerce' ),
		'NC'  => __( 'Northern Cape', 'woocommerce' ),
		'NW'  => __( 'North West', 'woocommerce' ),
		'WC'  => __( 'Western Cape', 'woocommerce' ),
	),
);
