<?php
/**
 * Peru states
 *
 * @package WooCommerce/i18n
 * @version 2.1.0
 */

global $states;

defined( 'ABSPATH' ) || exit;

$states['PE'] = array(
	'CAL' => __( 'El Callao', 'woocommerce' ),
	'LMA' => __( 'Municipalidad Metropolitana de Lima', 'woocommerce' ),
	'AMA' => __( 'Amazonas', 'woocommerce' ),
	'ANC' => __( 'Ancash', 'woocommerce' ),
	'APU' => __( 'Apur&iacute;mac', 'woocommerce' ),
	'ARE' => __( 'Arequipa', 'woocommerce' ),
	'AYA' => __( 'Ayacucho', 'woocommerce' ),
	'CAJ' => __( 'Cajamarca', 'woocommerce' ),
	'CUS' => __( 'Cusco', 'woocommerce' ),
	'HUV' => __( 'Huancavelica', 'woocommerce' ),
	'HUC' => __( 'Hu&aacute;nuco', 'woocommerce' ),
	'ICA' => __( 'Ica', 'woocommerce' ),
	'JUN' => __( 'Jun&iacute;n', 'woocommerce' ),
	'LAL' => __( 'La Libertad', 'woocommerce' ),
	'LAM' => __( 'Lambayeque', 'woocommerce' ),
	'LIM' => __( 'Lima', 'woocommerce' ),
	'LOR' => __( 'Loreto', 'woocommerce' ),
	'MDD' => __( 'Madre de Dios', 'woocommerce' ),
	'MOQ' => __( 'Moquegua', 'woocommerce' ),
	'PAS' => __( 'Pasco', 'woocommerce' ),
	'PIU' => __( 'Piura', 'woocommerce' ),
	'PUN' => __( 'Puno', 'woocommerce' ),
	'SAM' => __( 'San Mart&iacute;n', 'woocommerce' ),
	'TAC' => __( 'Tacna', 'woocommerce' ),
	'TUM' => __( 'Tumbes', 'woocommerce' ),
	'UCA' => __( 'Ucayali', 'woocommerce' ),
);
