<?php
/**
 * Hungary states
 *
 * @package WooCommerce/i18n
 * @version 2.0.0
 */

global $states;

defined( 'ABSPATH' ) || exit;

$states['HU'] = array(
	'BK' => __( '<PERSON><PERSON>cs-Kiskun', 'woocommerce' ),
	'BE' => __( 'Bék<PERSON>', 'woocommerce' ),
	'BA' => __( 'Baranya', 'woocommerce' ),
	'BZ' => __( 'Borsod-Abaúj-Zemplén', 'woocommerce' ),
	'BU' => __( 'Budapest', 'woocommerce' ),
	'CS' => __( 'Csongrád', 'woocommerce' ),
	'FE' => __( 'Fejér', 'woocommerce' ),
	'GS' => __( 'Győr-Moson-Sopron', 'woocommerce' ),
	'HB' => __( 'Hajdú-Bihar', 'woocommerce' ),
	'HE' => __( 'He<PERSON>', 'woocommerce' ),
	'JN' => __( 'Jász-Nagykun-Szolnok', 'woocommerce' ),
	'KE' => __( 'Komárom-Esztergom', 'woocommerce' ),
	'NO' => __( 'Nógrád', 'woocommerce' ),
	'PE' => __( 'Pest', 'woocommerce' ),
	'SO' => __( 'Somogy', 'woocommerce' ),
	'SZ' => __( 'Szabolcs-Szatmár-Bereg', 'woocommerce' ),
	'TO' => __( 'Tolna', 'woocommerce' ),
	'VA' => __( 'Vas', 'woocommerce' ),
	'VE' => __( 'Veszprém', 'woocommerce' ),
	'ZA' => __( 'Zala', 'woocommerce' ),
);
