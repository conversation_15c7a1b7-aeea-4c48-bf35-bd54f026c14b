<?php
/**
 * Greek Regions
 *
 * @package WooCommerce/i18n
 * @version 2.3.0
 */

global $states;

defined( 'ABSPATH' ) || exit;

$states['GR'] = array(
	'I' => __( 'At<PERSON>ki', 'woocommerce' ),
	'A' => __( 'Anatoliki Makedonia kai <PERSON>', 'woocommerce' ),
	'B' => __( 'Kentriki <PERSON>donia', 'woocommerce' ),
	'C' => __( 'Dytiki Makedonia', 'woocommerce' ),
	'D' => __( 'Ipeiros', 'woocommerce' ),
	'E' => __( 'Thessalia', 'woocommerce' ),
	'F' => __( 'Ion<PERSON>', 'woocommerce' ),
	'G' => __( 'Dytiki Ellada', 'woocommerce' ),
	'H' => __( 'Sterea Ellada', 'woocommerce' ),
	'J' => __( 'Peloponnis<PERSON>', 'woocommerce' ),
	'K' => __( 'Voreio Aigaio', 'woocommerce' ),
	'L' => __( 'Notio Aigaio', 'woocommerce' ),
	'M' => __( 'Kriti', 'woocommerce' ),
);
