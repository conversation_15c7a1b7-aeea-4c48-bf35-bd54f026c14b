!function(a){new(Backbone.View.extend({el:a("#key-fields"),events:{"click input#update_api_key":"saveKey"},initialize:function(){_.bindAll(this,"saveKey")},block:function(){a(this.el).block({message:null,overlayCSS:{background:"#fff",opacity:.6}})},unblock:function(){a(this.el).unblock()},initTipTip:function(i){a(document.body).on("click",i,function(e){e.preventDefault(),document.queryCommandSupported("copy")?(a("#copy-error").text(""),wcClearClipboard(),wcSetClipboard(a.trim(a(this).prev("input").val()),a(i))):(a(i).parent().find("input").focus().select(),a("#copy-error").text(woocommerce_admin_api_keys.clipboard_failed))}).on("aftercopy",i,function(){a("#copy-error").text(""),a(i).tipTip({attribute:"data-tip",activation:"focus",fadeIn:50,fadeOut:50,delay:0}).focus()}).on("aftercopyerror",i,function(){a(i).parent().find("input").focus().select(),a("#copy-error").text(woocommerce_admin_api_keys.clipboard_failed)})},createQRCode:function(e,i){a("#keys-qrcode").qrcode({text:e+"|"+i,width:120,height:120})},saveKey:function(e){e.preventDefault();var t=this;t.block(),Backbone.ajax({method:"POST",dataType:"json",url:woocommerce_admin_api_keys.ajax_url,data:{action:"woocommerce_update_api_key",security:woocommerce_admin_api_keys.update_api_nonce,key_id:a("#key_id",t.el).val(),description:a("#key_description",t.el).val(),user:a("#key_user",t.el).val(),permissions:a("#key_permissions",t.el).val()},success:function(e){if(a(".wc-api-message",t.el).remove(),e.success){var i=e.data;if(a("h2, h3",t.el).first().append('<div class="wc-api-message updated"><p>'+i.message+"</p></div>"),0<i.consumer_key.length&&0<i.consumer_secret.length){a("#api-keys-options",t.el).remove(),a("p.submit",t.el).empty().append(i.revoke_url);var o=wp.template("api-keys-template");a("p.submit",t.el).before(o({consumer_key:i.consumer_key,consumer_secret:i.consumer_secret})),t.createQRCode(i.consumer_key,i.consumer_secret),t.initTipTip(".copy-key"),t.initTipTip(".copy-secret")}else a("#key_description",t.el).val(i.description),a("#key_user",t.el).val(i.user_id),a("#key_permissions",t.el).val(i.permissions)}else a("h2, h3",t.el).first().append('<div class="wc-api-message error"><p>'+e.data.message+"</p></div>");t.unblock()}})}}))}(jQuery);