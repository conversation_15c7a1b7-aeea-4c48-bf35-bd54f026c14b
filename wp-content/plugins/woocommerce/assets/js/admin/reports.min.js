jQuery(function(f){function r(t,e,a){f('<div class="chart-tooltip">'+a+"</div>").css({top:e-16,left:t+20}).appendTo("body").fadeIn(200)}var o=null,s=null;f(".chart-placeholder").bind("plothover",function(t,e,a){if(a){if((o!==a.dataIndex||s!==a.seriesIndex)&&(o=a.dataIndex,s=a.seriesIndex,f(".chart-tooltip").remove(),a.series.points.show||a.series.enable_tooltip)){var n=a.series.data[a.dataIndex][1],i="";a.series.prepend_label&&(i=i+a.series.label+": "),a.series.prepend_tooltip&&(i+=a.series.prepend_tooltip),i+=n,a.series.append_tooltip&&(i+=a.series.append_tooltip),a.series.pie.show?r(e.pageX,e.pageY,i):r(a.pageX,a.pageY,i)}}else f(".chart-tooltip").remove(),o=null}),f(".wc_sparkline.bars").each(function(){var t=[{data:f(this).data("sparkline"),color:f(this).data("color"),bars:{fillColor:f(this).data("color"),fill:!0,show:!0,lineWidth:1,barWidth:f(this).data("barwidth"),align:"center"},shadowSize:0}];f.plot(f(this),t,{grid:{show:!1}})}),f(".wc_sparkline.lines").each(function(){var t=[{data:f(this).data("sparkline"),color:f(this).data("color"),lines:{fill:!1,show:!0,lineWidth:1,align:"center"},shadowSize:0}];f.plot(f(this),t,{grid:{show:!1}})});var a=f(".range_datepicker").datepicker({changeMonth:!0,changeYear:!0,defaultDate:"",dateFormat:"yy-mm-dd",numberOfMonths:1,minDate:"-20Y",maxDate:"+1D",showButtonPanel:!0,showOn:"focus",buttonImageOnly:!0,onSelect:function(){var t=f(this).is(".from")?"minDate":"maxDate",e=f(this).datepicker("getDate");a.not(this).datepicker("option",t,e)}});"undefined"==typeof document.createElement("a").download&&f(".export_csv").hide(),f(".export_csv").click(function(){var a=f(this).data("exclude_series")||"";a=(a=a.toString()).split(",");var t,e,n,i=f(this).data("xaxes"),r=f(this).data("groupby"),o=f(this).data("index_type"),s=f(this).data("export"),l="";if("table"===s)f(this).offsetParent().find("thead tr,tbody tr").each(function(){f(this).find("th, td").each(function(){var t=f(this).text();t=t.replace("[?]","").replace("#",""),l+='"'+t+'",'}),l=l.substring(0,l.length-1),l+="\n"}),f(this).offsetParent().find("tfoot tr").each(function(){f(this).find("th, td").each(function(){var t=f(this).text();if(t=t.replace("[?]","").replace("#",""),l+='"'+t+'",',0<f(this).attr("colspan"))for(p=1;p<f(this).attr("colspan");p++)l+='"",'}),l=l.substring(0,l.length-1),l+="\n"});else{if(!window.main_chart)return!1;var h=window.main_chart.getData(),d=[];for(l+='"'+i+'",',f.each(h,function(t,e){a&&-1!==f.inArray(t.toString(),a)||d.push(e)}),t=0;t<d.length;++t)l+='"'+d[t].label+'",';l=l.substring(0,l.length-1),l+="\n";var c={};for(t=0;t<d.length;++t)for(e=d[t].data,n=0;n<e.length;++n){c[e[n][0]]=[];for(var p=0;p<d.length;++p)c[e[n][0]].push(0)}for(t=0;t<d.length;++t)for(e=d[t].data,n=0;n<e.length;++n)c[e[n][0]][t]=e[n][1];f.each(c,function(t,e){var a=new Date(parseInt(t,10));l+="none"===o?'"'+t+'",':"day"===r?'"'+a.getUTCFullYear()+"-"+parseInt(a.getUTCMonth()+1,10)+"-"+a.getUTCDate()+'",':'"'+a.getUTCFullYear()+"-"+parseInt(a.getUTCMonth()+1,10)+'",';for(var n=0;n<e.length;++n){var i=e[n];Math.round(i)!==i&&(i=(i=parseFloat(i)).toFixed(2)),l+='"'+i+'",'}l=l.substring(0,l.length-1),l+="\n"})}return l="data:text/csv;charset=utf-8,\ufeff"+encodeURIComponent(l),f(this).attr("href",l),!0})});