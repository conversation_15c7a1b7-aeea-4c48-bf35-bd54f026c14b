jQuery(function(g){var f={states:null,init:function(){"undefined"!=typeof wc_users_params.countries&&(this.states=g.parseJSON(wc_users_params.countries.replace(/&quot;/g,'"'))),g(".js_field-country").selectWoo().change(this.change_country),g(".js_field-country").trigger("change",[!0]),g(document.body).on("change","select.js_field-state",this.change_state),g(document.body).on("click","button.js_copy-billing",this.copy_billing)},change_country:function(e,t){if(void 0===t&&(t=!1),null!==f.states){var a,n=g(this),o=n.val(),i=n.parents(".form-table").find(":input.js_field-state"),c=i.parent(),s=i.attr("name"),l=i.attr("id"),r="woocommerce.stickState-"+o,p=n.data(r)?n.data(r):i.val(),d=i.attr("placeholder");if(t&&n.data("woocommerce.stickState-"+o,p),c.show().find(".select2-container").remove(),g.isEmptyObject(f.states[o]))a=g('<input type="text" />').prop("id",l).prop("name",s).prop("placeholder",d).addClass("js_field-state regular-text").val(p),i.replaceWith(a);else{var u=f.states[o],h=g('<option value=""></option>').text(f.i18n_select_state_text);a=g('<select style="width: 25em;"></select>').prop("id",l).prop("name",s).prop("placeholder",d).addClass("js_field-state").append(h),g.each(u,function(e){var t=g("<option></option>").prop("value",e).text(u[e]);a.append(t)}),a.val(p),i.replaceWith(a),a.show().selectWoo().hide().change()}g(document.body).trigger("contry-change.woocommerce",[o,g(this).closest("div")]),g(document.body).trigger("country-change.woocommerce",[o,g(this).closest("div")])}},change_state:function(){var e=g(this),t=e.val(),a=e.parents(".form-table").find(":input.js_field-country"),n=a.val();a.data("woocommerce.stickState-"+n,t)},copy_billing:function(e){e.preventDefault(),g("#fieldset-billing").find("input, select").each(function(e,t){var a=t.name.replace(/^billing_/,"shipping_"),n=g('[name="'+a+'"]');n.length&&n.val(t.value).trigger("change")})}};f.init()});