!function(s,r,a){s(function(){s("select#woocommerce_allowed_countries").change(function(){"specific"===s(this).val()?(s(this).closest("tr").next("tr").hide(),s(this).closest("tr").next().next("tr").show()):("all_except"===s(this).val()?s(this).closest("tr").next("tr").show():s(this).closest("tr").next("tr").hide(),s(this).closest("tr").next().next("tr").hide())}).change(),s("select#woocommerce_ship_to_countries").change(function(){"specific"===s(this).val()?s(this).closest("tr").next("tr").show():s(this).closest("tr").next("tr").hide()}).change(),s("input#woocommerce_manage_stock").change(function(){s(this).is(":checked")?s(this).closest("tbody").find(".manage_stock_field").closest("tr").show():s(this).closest("tbody").find(".manage_stock_field").closest("tr").hide()}).change(),s(".colorpick").iris({change:function(e,t){s(this).parent().find(".colorpickpreview").css({backgroundColor:t.color.toString()})},hide:!0,border:!0}).on("click focus",function(e){e.stopPropagation(),s(".iris-picker").hide(),s(this).closest("td").find(".iris-picker").show(),s(this).data("original-value",s(this).val())}).on("change",function(){s(this).is(".iris-error")&&(s(this).data("original-value").match(/^\#([a-fA-F0-9]{6}|[a-fA-F0-9]{3})$/)?s(this).val(s(this).data("original-value")).change():s(this).val("").change())}),s("body").on("click",function(){s(".iris-picker").hide()}),s(function(){var e=!1;s("input, textarea, select, checkbox").change(function(){e=!0}),s(".woo-nav-tab-wrapper a").click(function(){window.onbeforeunload=e?function(){return r.i18n_nav_warning}:""}),s(".submit :input").click(function(){window.onbeforeunload=""})}),s("table.wc_gateways tbody, table.wc_shipping tbody").sortable({items:"tr",cursor:"move",axis:"y",handle:"td.sort",scrollSensitivity:40,helper:function(e,t){return t.children().each(function(){s(this).width(s(this).width())}),t.css("left","0"),t},start:function(e,t){t.item.css("background-color","#f6f6f6")},stop:function(e,t){t.item.removeAttr("style"),t.item.trigger("updateMoveButtons")}}),s(".woocommerce").on("click",".select_all",function(){return s(this).closest("td").find("select option").attr("selected","selected"),s(this).closest("td").find("select").trigger("change"),!1}),s(".woocommerce").on("click",".select_none",function(){return s(this).closest("td").find("select option").removeAttr("selected"),s(this).closest("td").find("select").trigger("change"),!1}),s(".wc-item-reorder-nav").find(".wc-move-up, .wc-move-down").on("click",function(){var e=s(this),t=e.closest("tr");e.focus();var i=e.is(".wc-move-up"),o=e.is(".wc-move-down");if(i){var c=t.prev("tr");c&&c.length&&(c.before(t),a.a11y.speak(r.i18n_moved_up))}else if(o){var n=t.next("tr");n&&n.length&&(n.after(t),a.a11y.speak(r.i18n_moved_down))}e.focus(),e.closest("table").trigger("updateMoveButtons")}),s(".wc-item-reorder-nav").closest("table").on("updateMoveButtons",function(){var e=s(this),t=s(this).find("tbody tr:last"),i=s(this).find("tbody tr:first");e.find(".wc-item-reorder-nav .wc-move-disabled").removeClass("wc-move-disabled").attr({tabindex:"0","aria-hidden":"false"}),i.find(".wc-item-reorder-nav .wc-move-up").addClass("wc-move-disabled").attr({tabindex:"-1","aria-hidden":"true"}),t.find(".wc-item-reorder-nav .wc-move-down").addClass("wc-move-disabled").attr({tabindex:"-1","aria-hidden":"true"})}),s(".wc-item-reorder-nav").closest("table").trigger("updateMoveButtons"),s(".submit button").on("click",function(){if("specific"===s("select#woocommerce_allowed_countries").val()&&!s('[name="woocommerce_specific_allowed_countries[]"]').val())return!!window.confirm(woocommerce_settings_params.i18n_no_specific_countries_selected)})})}(jQuery,woocommerce_settings_params,wp);