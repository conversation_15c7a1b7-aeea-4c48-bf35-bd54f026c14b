!function(c,r,p,l){c(function(){var s=c(".wc-shipping-zone-methods"),e=c(".wc-shipping-zone-method-rows"),n=c(".wc-shipping-zone-method-save"),t=p.template("wc-shipping-zone-method-row"),o=p.template("wc-shipping-zone-method-row-blank"),i=Backbone.Model.extend({changes:{},logChanges:function(e){var t=this.changes||{};_.each(e.methods,function(e,n){t.methods=t.methods||{methods:{}},t.methods[n]=_.extend(t.methods[n]||{instance_id:n},e)}),"undefined"!=typeof e.zone_name&&(t.zone_name=e.zone_name),"undefined"!=typeof e.zone_locations&&(t.zone_locations=e.zone_locations),"undefined"!=typeof e.zone_postcodes&&(t.zone_postcodes=e.zone_postcodes),this.changes=t,this.trigger("change:methods")},save:function(){c.post(l+(0<l.indexOf("?")?"&":"?")+"action=woocommerce_shipping_zone_methods_save_changes",{wc_shipping_zones_nonce:r.wc_shipping_zones_nonce,changes:this.changes,zone_id:r.zone_id},this.onSaveResponse,"json")},onSaveResponse:function(e,n){"success"===n&&(e.success?(e.data.zone_id!==r.zone_id&&(r.zone_id=e.data.zone_id,window.history.pushState&&window.history.pushState({},"","admin.php?page=wc-settings&tab=shipping&zone_id="+e.data.zone_id)),a.set("methods",e.data.methods),a.trigger("change:methods"),a.changes={},a.trigger("saved:methods")):window.alert(r.strings.save_failed))}}),d=Backbone.View.extend({rowTemplate:t,initialize:function(){this.listenTo(this.model,"change:methods",this.setUnloadConfirmation),this.listenTo(this.model,"saved:methods",this.clearUnloadConfirmation),this.listenTo(this.model,"saved:methods",this.render),e.on("change",{view:this},this.updateModelOnChange),e.on("sortupdate",{view:this},this.updateModelOnSort),c(window).on("beforeunload",{view:this},this.unloadConfirmation),n.on("click",{view:this},this.onSubmit),c(document.body).on("input change","#zone_name, #zone_locations, #zone_postcodes",{view:this},this.onUpdateZone),c(document.body).on("click",".wc-shipping-zone-method-settings",{view:this},this.onConfigureShippingMethod),c(document.body).on("click",".wc-shipping-zone-add-method",{view:this},this.onAddShippingMethod),c(document.body).on("wc_backbone_modal_response",this.onConfigureShippingMethodSubmitted),c(document.body).on("wc_backbone_modal_response",this.onAddShippingMethodSubmitted),c(document.body).on("change",".wc-shipping-zone-method-selector select",this.onChangeShippingMethodSelector),c(document.body).on("click",".wc-shipping-zone-postcodes-toggle",this.onTogglePostcodes)},onUpdateZone:function(e){var n=e.data.view,t=n.model,o=c(this).val(),i=c(e.target).data("attribute"),s={};e.preventDefault(),s[i]=o,t.set(i,o),t.logChanges(s),n.render()},block:function(){c(this.el).block({message:null,overlayCSS:{background:"#fff",opacity:.6}})},unblock:function(){c(this.el).unblock()},render:function(){var e=_.indexBy(this.model.get("methods"),"instance_id"),n=this.model.get("zone_name"),i=this;c(".wc-shipping-zone-name").text(n||r.strings.default_zone_name),this.$el.empty(),this.unblock(),_.size(e)?(e=_.sortBy(e,function(e){return parseInt(e.method_order,10)}),c.each(e,function(e,n){"yes"===n.enabled?n.enabled_icon='<span class="woocommerce-input-toggle woocommerce-input-toggle--enabled">'+r.strings.yes+"</span>":n.enabled_icon='<span class="woocommerce-input-toggle woocommerce-input-toggle--disabled">'+r.strings.no+"</span>",i.$el.append(i.rowTemplate(n));var t=i.$el.find('tr[data-id="'+n.instance_id+'"]');if(!n.has_settings){t.find(".wc-shipping-zone-method-title > a").replaceWith("<span>"+t.find(".wc-shipping-zone-method-title > a").text()+"</span>");var o=t.find(".wc-shipping-zone-method-delete");t.find(".wc-shipping-zone-method-title .row-actions").empty().html(o)}}),this.$el.find(".wc-shipping-zone-method-delete").on("click",{view:this},this.onDeleteRow),this.$el.find(".wc-shipping-zone-method-enabled a").on("click",{view:this},this.onToggleEnabled)):i.$el.append(o),this.initTooltips()},initTooltips:function(){c("#tiptip_holder").removeAttr("style"),c("#tiptip_arrow").removeAttr("style"),c(".tips").tipTip({attribute:"data-tip",fadeIn:50,fadeOut:50,delay:50})},onSubmit:function(e){e.data.view.block(),e.data.view.model.save(),e.preventDefault()},onDeleteRow:function(e){var n=e.data.view,t=n.model,o=_.indexBy(t.get("methods"),"instance_id"),i={},s=c(this).closest("tr").data("id");e.preventDefault(),delete o[s],i.methods=i.methods||{methods:{}},i.methods[s]=_.extend(i.methods[s]||{},{deleted:"deleted"}),t.set("methods",o),t.logChanges(i),n.render()},onToggleEnabled:function(e){var n=e.data.view,t=c(e.target),o=n.model,i=_.indexBy(o.get("methods"),"instance_id"),s=t.closest("tr").data("id"),d="yes"===t.closest("tr").data("enabled")?"no":"yes",a={};e.preventDefault(),i[s].enabled=d,a.methods=a.methods||{methods:{}},a.methods[s]=_.extend(a.methods[s]||{},{enabled:d}),o.set("methods",i),o.logChanges(a),n.render()},setUnloadConfirmation:function(){this.needsUnloadConfirm=!0,n.removeAttr("disabled")},clearUnloadConfirmation:function(){this.needsUnloadConfirm=!1,n.attr("disabled","disabled")},unloadConfirmation:function(e){if(e.data.view.needsUnloadConfirm)return e.returnValue=r.strings.unload_confirmation_msg,window.event.returnValue=r.strings.unload_confirmation_msg,r.strings.unload_confirmation_msg},updateModelOnChange:function(e){var n=e.data.view.model,t=c(e.target),o=t.closest("tr").data("id"),i=t.data("attribute"),s=t.val(),d=_.indexBy(n.get("methods"),"instance_id"),a={};d[o][i]!==s&&(a.methods[o]={},a.methods[o][i]=s,d[o][i]=s),n.logChanges(a)},updateModelOnSort:function(e){var n=e.data.view.model,o=_.indexBy(n.get("methods"),"instance_id"),i={};_.each(o,function(e){var n=parseInt(e.method_order,10),t=parseInt(s.find('tr[data-id="'+e.instance_id+'"]').index()+1,10);n!==t&&(o[e.instance_id].method_order=t,i.methods=i.methods||{methods:{}},i.methods[e.instance_id]=_.extend(i.methods[e.instance_id]||{},{method_order:t}))}),_.size(i)&&n.logChanges(i)},onConfigureShippingMethod:function(e){var n=c(this).closest("tr").data("id"),t=e.data.view.model,o=_.indexBy(t.get("methods"),"instance_id")[n];if(!o.settings_html)return!0;e.preventDefault(),c(this).WCBackboneModal({template:"wc-modal-shipping-method-settings",variable:{instance_id:n,method:o},data:{instance_id:n,method:o}}),c(document.body).trigger("init_tooltips")},onConfigureShippingMethodSubmitted:function(e,n,t){"wc-modal-shipping-method-settings"===n&&(h.block(),c.post(l+(0<l.indexOf("?")?"&":"?")+"action=woocommerce_shipping_zone_methods_save_settings",{wc_shipping_zones_nonce:r.wc_shipping_zones_nonce,instance_id:t.instance_id,data:t},function(e,n){"success"===n&&e.success?(c("table.wc-shipping-zone-methods").parent().find("#woocommerce_errors").remove(),0<e.data.errors.length&&h.showErrors(e.data.errors),_.size(h.model.changes)?h.model.save():h.model.onSaveResponse(e,n)):(window.alert(r.strings.save_failed),h.unblock())},"json"))},showErrors:function(e){var t='<div id="woocommerce_errors" class="error notice is-dismissible">';c(e).each(function(e,n){t=t+"<p>"+n+"</p>"}),t+="</div>",c("table.wc-shipping-zone-methods").before(t)},onAddShippingMethod:function(e){e.preventDefault(),c(this).WCBackboneModal({template:"wc-modal-add-shipping-method",variable:{zone_id:r.zone_id}}),c(".wc-shipping-zone-method-selector select").change()},onAddShippingMethodSubmitted:function(e,n,t){"wc-modal-add-shipping-method"===n&&(h.block(),c.post(l+(0<l.indexOf("?")?"&":"?")+"action=woocommerce_shipping_zone_add_method",{wc_shipping_zones_nonce:r.wc_shipping_zones_nonce,method_id:t.add_method_id,zone_id:r.zone_id},function(e,n){"success"===n&&e.success&&(e.data.zone_id!==r.zone_id&&(r.zone_id=e.data.zone_id,window.history.pushState&&window.history.pushState({},"","admin.php?page=wc-settings&tab=shipping&zone_id="+e.data.zone_id)),_.size(h.model.changes)?h.model.save():(h.model.set("methods",e.data.methods),h.model.trigger("change:methods"),h.model.changes={},h.model.trigger("saved:methods"))),h.unblock()},"json"))},onChangeShippingMethodSelector:function(){var e=c(this).find("option:selected").data("description");c(this).parent().find(".wc-shipping-zone-method-description").remove(),c(this).after('<div class="wc-shipping-zone-method-description">'+e+"</div>"),c(this).closest("article").height(c(this).parent().height())},onTogglePostcodes:function(e){e.preventDefault();var n=c(this).closest("tr");n.find(".wc-shipping-zone-postcodes").show(),n.find(".wc-shipping-zone-postcodes-toggle").hide()}}),a=new i({methods:r.methods,zone_name:r.zone_name}),h=new d({model:a,el:e});h.render(),e.sortable({items:"tr",cursor:"move",axis:"y",handle:"td.wc-shipping-zone-method-sort",scrollSensitivity:40})})}(jQuery,shippingZoneMethodsLocalizeScript,wp,ajaxurl);