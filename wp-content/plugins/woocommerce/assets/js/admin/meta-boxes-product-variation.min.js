jQuery(function(d){"use strict";var o={init:function(){d("#variable_product_options").on("change","input.variable_is_downloadable",this.variable_is_downloadable).on("change","input.variable_is_virtual",this.variable_is_virtual).on("change","input.variable_manage_stock",this.variable_manage_stock).on("click","button.notice-dismiss",this.notice_dismiss).on("click","h3 .sort",this.set_menu_order).on("reload",this.reload),d("input.variable_is_downloadable, input.variable_is_virtual, input.variable_manage_stock").change(),d("#woocommerce-product-data").on("woocommerce_variations_loaded",this.variations_loaded),d(document.body).on("woocommerce_variations_added",this.variation_added)},reload:function(){n.load_variations(1),m.set_paginav(0)},variable_is_downloadable:function(){d(this).closest(".woocommerce_variation").find(".show_if_variation_downloadable").hide(),d(this).is(":checked")&&d(this).closest(".woocommerce_variation").find(".show_if_variation_downloadable").show()},variable_is_virtual:function(){d(this).closest(".woocommerce_variation").find(".hide_if_variation_virtual").show(),d(this).is(":checked")&&d(this).closest(".woocommerce_variation").find(".hide_if_variation_virtual").hide()},variable_manage_stock:function(){d(this).closest(".woocommerce_variation").find(".show_if_variation_manage_stock").hide(),d(this).closest(".woocommerce_variation").find(".variable_stock_status").show(),d(this).is(":checked")&&(d(this).closest(".woocommerce_variation").find(".show_if_variation_manage_stock").show(),d(this).closest(".woocommerce_variation").find(".variable_stock_status").hide()),d("input#_manage_stock:checked").length&&d(this).closest(".woocommerce_variation").find(".variable_stock_status").hide()},notice_dismiss:function(){d(this).closest("div.notice").remove()},variations_loaded:function(a,e){e=e||!1;var i=d("#woocommerce-product-data");e||(d("input.variable_is_downloadable, input.variable_is_virtual, input.variable_manage_stock",i).change(),d(".woocommerce_variation",i).each(function(a,e){var i=d(e),o=d(".sale_price_dates_from",i).val(),t=d(".sale_price_dates_to",i).val();""===o&&""===t||d("a.sale_schedule",i).click()}),d(".woocommerce_variations .variation-needs-update",i).removeClass("variation-needs-update"),d("button.cancel-variation-changes, button.save-variation-changes",i).attr("disabled","disabled")),d("#tiptip_holder").removeAttr("style"),d("#tiptip_arrow").removeAttr("style"),d(".woocommerce_variations .tips, .woocommerce_variations .help_tip, .woocommerce_variations .woocommerce-help-tip",i).tipTip({attribute:"data-tip",fadeIn:50,fadeOut:50,delay:200}),d(".sale_price_dates_fields",i).find("input").datepicker({defaultDate:"",dateFormat:"yy-mm-dd",numberOfMonths:1,showButtonPanel:!0,onSelect:function(){var a=d(this).is(".sale_price_dates_from")?"minDate":"maxDate",e=d(this).closest(".sale_price_dates_fields").find("input"),i=d(this).datepicker("getDate");e.not(this).datepicker("option",a,i),d(this).change()}}),d(".woocommerce_variations",i).sortable({items:".woocommerce_variation",cursor:"move",axis:"y",handle:".sort",scrollSensitivity:40,forcePlaceholderSize:!0,helper:"clone",opacity:.65,stop:function(){o.variation_row_indexes()}}),d(document.body).trigger("wc-enhanced-select-init")},variation_added:function(a,e){1===e&&o.variations_loaded(null,!0)},set_menu_order:function(a){a.preventDefault();var e=d(this).closest(".woocommerce_variation").find(".variation_menu_order"),i=window.prompt(woocommerce_admin_meta_boxes_variations.i18n_enter_menu_order,e.val());null!=i&&(e.val(parseInt(i,10)).change(),n.save_variations())},variation_row_indexes:function(){var a=d("#variable_product_options").find(".woocommerce_variations"),e=parseInt(a.attr("data-page"),10),i=parseInt((e-1)*woocommerce_admin_meta_boxes_variations.variations_per_page,10);d(".woocommerce_variations .woocommerce_variation").each(function(a,e){d(".variation_menu_order",e).val(parseInt(d(e).index(".woocommerce_variations .woocommerce_variation"),10)+1+i).change()})}},t={variable_image_frame:null,setting_variation_image_id:null,setting_variation_image:null,wp_media_post_id:wp.media.model.settings.post.id,init:function(){d("#variable_product_options").on("click",".upload_image_button",this.add_image),d("a.add_media").on("click",this.restore_wp_media_post_id)},add_image:function(a){var e=d(this),i=e.attr("rel"),o=e.closest(".upload_image");if(t.setting_variation_image=o,t.setting_variation_image_id=i,a.preventDefault(),e.is(".remove"))d(".upload_image_id",t.setting_variation_image).val("").change(),t.setting_variation_image.find("img").eq(0).attr("src",woocommerce_admin_meta_boxes_variations.woocommerce_placeholder_img_src),t.setting_variation_image.find(".upload_image_button").removeClass("remove");else{if(t.variable_image_frame)return t.variable_image_frame.uploader.uploader.param("post_id",t.setting_variation_image_id),void t.variable_image_frame.open();wp.media.model.settings.post.id=t.setting_variation_image_id,t.variable_image_frame=wp.media.frames.variable_image=wp.media({title:woocommerce_admin_meta_boxes_variations.i18n_choose_image,button:{text:woocommerce_admin_meta_boxes_variations.i18n_set_image},states:[new wp.media.controller.Library({title:woocommerce_admin_meta_boxes_variations.i18n_choose_image,filterable:"all"})]}),t.variable_image_frame.on("select",function(){var a=t.variable_image_frame.state().get("selection").first().toJSON(),e=a.sizes&&a.sizes.thumbnail?a.sizes.thumbnail.url:a.url;d(".upload_image_id",t.setting_variation_image).val(a.id).change(),t.setting_variation_image.find(".upload_image_button").addClass("remove"),t.setting_variation_image.find("img").eq(0).attr("src",e),wp.media.model.settings.post.id=t.wp_media_post_id}),t.variable_image_frame.open()}},restore_wp_media_post_id:function(){wp.media.model.settings.post.id=t.wp_media_post_id}},n={init:function(){d("li.variations_tab a").on("click",this.initial_load),d("#variable_product_options").on("click","button.save-variation-changes",this.save_variations).on("click","button.cancel-variation-changes",this.cancel_variations).on("click",".remove_variation",this.remove_variation).on("click",".downloadable_files a.delete",this.input_changed),d(document.body).on("change","#variable_product_options .woocommerce_variations :input",this.input_changed).on("change",".variations-defaults select",this.defaults_changed);var a=d("form#post");a.on("submit",this.save_on_submit),d("input:submit",a).bind("click keypress",function(){a.data("callerid",this.id)}),d(".wc-metaboxes-wrapper").on("click","a.do_variation_action",this.do_variation_action)},check_for_changes:function(){var a=d("#variable_product_options").find(".woocommerce_variations .variation-needs-update");if(0<a.length){if(!window.confirm(woocommerce_admin_meta_boxes_variations.i18n_edited_variations))return a.removeClass("variation-needs-update"),!1;n.save_changes()}return!0},block:function(){d("#woocommerce-product-data").block({message:null,overlayCSS:{background:"#fff",opacity:.6}})},unblock:function(){d("#woocommerce-product-data").unblock()},initial_load:function(){0===d("#variable_product_options").find(".woocommerce_variations .woocommerce_variation").length&&m.go_to_page()},load_variations:function(e,a){e=e||1,a=a||woocommerce_admin_meta_boxes_variations.variations_per_page;var i=d("#variable_product_options").find(".woocommerce_variations");n.block(),d.ajax({url:woocommerce_admin_meta_boxes_variations.ajax_url,data:{action:"woocommerce_load_variations",security:woocommerce_admin_meta_boxes_variations.load_variations_nonce,product_id:woocommerce_admin_meta_boxes_variations.post_id,attributes:i.data("attributes"),page:e,per_page:a},type:"POST",success:function(a){i.empty().append(a).attr("data-page",e),d("#woocommerce-product-data").trigger("woocommerce_variations_loaded"),n.unblock()}})},get_variations_fields:function(a){var o=d(":input",a).serializeJSON();return d(".variations-defaults select").each(function(a,e){var i=d(e);o[i.attr("name")]=i.val()}),o},save_changes:function(e){var a=d("#variable_product_options").find(".woocommerce_variations"),i=d(".variation-needs-update",a),o={};0<i.length&&(n.block(),(o=n.get_variations_fields(i)).action="woocommerce_save_variations",o.security=woocommerce_admin_meta_boxes_variations.save_variations_nonce,o.product_id=woocommerce_admin_meta_boxes_variations.post_id,o["product-type"]=d("#product-type").val(),d.ajax({url:woocommerce_admin_meta_boxes_variations.ajax_url,data:o,type:"POST",success:function(a){i.removeClass("variation-needs-update"),d("button.cancel-variation-changes, button.save-variation-changes").attr("disabled","disabled"),d("#woocommerce-product-data").trigger("woocommerce_variations_saved"),"function"==typeof e&&e(a),n.unblock()}}))},save_variations:function(){return d("#variable_product_options").trigger("woocommerce_variations_save_variations_button"),n.save_changes(function(a){var e=d("#variable_product_options").find(".woocommerce_variations"),i=e.attr("data-page");d("#variable_product_options").find("#woocommerce_errors").remove(),a&&e.before(a),d(".variations-defaults select").each(function(){d(this).attr("data-current",d(this).val())}),m.go_to_page(i)}),!1},save_on_submit:function(a){0<d("#variable_product_options").find(".woocommerce_variations .variation-needs-update").length&&(a.preventDefault(),d("#variable_product_options").trigger("woocommerce_variations_save_variations_on_submit"),n.save_changes(n.save_on_submit_done))},save_on_submit_done:function(){var a=d("form#post");"publish"===a.data("callerid")?a.append('<input type="hidden" name="publish" value="1" />').submit():a.append('<input type="hidden" name="save-post" value="1" />').submit()},cancel_variations:function(){var a=parseInt(d("#variable_product_options").find(".woocommerce_variations").attr("data-page"),10);return d("#variable_product_options").find(".woocommerce_variations .variation-needs-update").removeClass("variation-needs-update"),d(".variations-defaults select").each(function(){d(this).val(d(this).attr("data-current"))}),m.go_to_page(a),!1},add_variation:function(){n.block();var a={action:"woocommerce_add_variation",post_id:woocommerce_admin_meta_boxes_variations.post_id,loop:d(".woocommerce_variation").length,security:woocommerce_admin_meta_boxes_variations.add_variation_nonce};return d.post(woocommerce_admin_meta_boxes_variations.ajax_url,a,function(a){var e=d(a);e.addClass("variation-needs-update"),d("#variable_product_options").find(".woocommerce_variations").prepend(e),d("button.cancel-variation-changes, button.save-variation-changes").removeAttr("disabled"),d("#variable_product_options").trigger("woocommerce_variations_added",1),n.unblock()}),!1},remove_variation:function(){if(n.check_for_changes(),window.confirm(woocommerce_admin_meta_boxes_variations.i18n_remove_variation)){var a=d(this).attr("rel"),e=[],i={action:"woocommerce_remove_variations"};n.block(),0<a?(e.push(a),i.variation_ids=e,i.security=woocommerce_admin_meta_boxes_variations.delete_variations_nonce,d.post(woocommerce_admin_meta_boxes_variations.ajax_url,i,function(){var a=d("#variable_product_options").find(".woocommerce_variations"),e=parseInt(a.attr("data-page"),10),i=Math.ceil((parseInt(a.attr("data-total"),10)-1)/woocommerce_admin_meta_boxes_variations.variations_per_page),o=1;d("#woocommerce-product-data").trigger("woocommerce_variations_removed"),e===i||e<=i?o=e:i<e&&0!==i&&(o=i),m.go_to_page(o,-1)})):n.unblock()}return!1},link_all_variations:function(){if(n.check_for_changes(),window.confirm(woocommerce_admin_meta_boxes_variations.i18n_link_all_variations)){n.block();var a={action:"woocommerce_link_all_variations",post_id:woocommerce_admin_meta_boxes_variations.post_id,security:woocommerce_admin_meta_boxes_variations.link_variation_nonce};d.post(woocommerce_admin_meta_boxes_variations.ajax_url,a,function(a){var e=parseInt(a,10);1===e?window.alert(e+" "+woocommerce_admin_meta_boxes_variations.i18n_variation_added):0===e||1<e?window.alert(e+" "+woocommerce_admin_meta_boxes_variations.i18n_variations_added):window.alert(woocommerce_admin_meta_boxes_variations.i18n_no_variations_added),0<e?(m.go_to_page(1,e),d("#variable_product_options").trigger("woocommerce_variations_added",e)):n.unblock()})}return!1},input_changed:function(){d(this).closest(".woocommerce_variation").addClass("variation-needs-update"),d("button.cancel-variation-changes, button.save-variation-changes").removeAttr("disabled"),d("#variable_product_options").trigger("woocommerce_variations_input_changed")},defaults_changed:function(){d(this).closest("#variable_product_options").find(".woocommerce_variation:first").addClass("variation-needs-update"),d("button.cancel-variation-changes, button.save-variation-changes").removeAttr("disabled"),d("#variable_product_options").trigger("woocommerce_variations_defaults_changed")},do_variation_action:function(){var a,e=d("select.variation_actions").val(),i={},o=0;switch(e){case"add_variation":return void n.add_variation();case"link_all_variations":return void n.link_all_variations();case"delete_all":window.confirm(woocommerce_admin_meta_boxes_variations.i18n_delete_all_variations)&&window.confirm(woocommerce_admin_meta_boxes_variations.i18n_last_warning)&&(i.allowed=!0,o=-1*parseInt(d("#variable_product_options").find(".woocommerce_variations").attr("data-total"),10));break;case"variable_regular_price_increase":case"variable_regular_price_decrease":case"variable_sale_price_increase":case"variable_sale_price_decrease":if(null==(a=window.prompt(woocommerce_admin_meta_boxes_variations.i18n_enter_a_value_fixed_or_percent)))return;0<=a.indexOf("%")?i.value=accounting.unformat(a.replace(/\%/,""),woocommerce_admin.mon_decimal_point)+"%":i.value=accounting.unformat(a,woocommerce_admin.mon_decimal_point);break;case"variable_regular_price":case"variable_sale_price":case"variable_stock":case"variable_weight":case"variable_length":case"variable_width":case"variable_height":case"variable_download_limit":case"variable_download_expiry":if(null==(a=window.prompt(woocommerce_admin_meta_boxes_variations.i18n_enter_a_value)))return;i.value=a;break;case"variable_sale_schedule":if(i.date_from=window.prompt(woocommerce_admin_meta_boxes_variations.i18n_scheduled_sale_start),i.date_to=window.prompt(woocommerce_admin_meta_boxes_variations.i18n_scheduled_sale_end),null===i.date_from&&(i.date_from=!1),null===i.date_to&&(i.date_to=!1),!1===i.date_to&&!1===i.date_from)return;break;default:d("select.variation_actions").trigger(e),i=d("select.variation_actions").triggerHandler(e+"_ajax_data",i)}"delete_all"===e&&i.allowed?d("#variable_product_options").find(".variation-needs-update").removeClass("variation-needs-update"):n.check_for_changes(),n.block(),d.ajax({url:woocommerce_admin_meta_boxes_variations.ajax_url,data:{action:"woocommerce_bulk_edit_variations",security:woocommerce_admin_meta_boxes_variations.bulk_edit_variations_nonce,product_id:woocommerce_admin_meta_boxes_variations.post_id,product_type:d("#product-type").val(),bulk_action:e,data:i},type:"POST",success:function(){m.go_to_page(1,o)}})}},m={init:function(){d(document.body).on("woocommerce_variations_added",this.update_single_quantity).on("change",".variations-pagenav .page-selector",this.page_selector).on("click",".variations-pagenav .first-page",this.first_page).on("click",".variations-pagenav .prev-page",this.prev_page).on("click",".variations-pagenav .next-page",this.next_page).on("click",".variations-pagenav .last-page",this.last_page)},update_variations_count:function(a){var e=d("#variable_product_options").find(".woocommerce_variations"),i=parseInt(e.attr("data-total"),10)+a,o=d(".variations-pagenav .displaying-num");return e.attr("data-total",i),1===i?o.text(woocommerce_admin_meta_boxes_variations.i18n_variation_count_single.replace("%qty%",i)):o.text(woocommerce_admin_meta_boxes_variations.i18n_variation_count_plural.replace("%qty%",i)),i},update_single_quantity:function(a,e){if(1===e){var i=d(".variations-pagenav");m.update_variations_count(e),i.is(":hidden")&&(d("option, optgroup",".variation_actions").show(),d(".variation_actions").val("add_variation"),d("#variable_product_options").find(".toolbar").show(),i.show(),d(".pagination-links",i).hide())}},set_paginav:function(a){var e=d("#variable_product_options").find(".woocommerce_variations"),i=m.update_variations_count(a),o=d("#variable_product_options").find(".toolbar"),t=d(".variation_actions"),n=d(".variations-pagenav"),r=d(".pagination-links",n),_=Math.ceil(i/woocommerce_admin_meta_boxes_variations.variations_per_page),s="";e.attr("data-total_pages",_),d(".total-pages",n).text(_);for(var c=1;c<=_;c++)s+='<option value="'+c+'">'+c+"</option>";d(".page-selector",n).empty().html(s),0===i?(o.not(".toolbar-top, .toolbar-buttons").hide(),n.hide(),d("option, optgroup",t).hide(),d(".variation_actions").val("add_variation"),d('option[data-global="true"]',t).show()):(o.show(),n.show(),d("option, optgroup",t).show(),d(".variation_actions").val("add_variation"),1===_?r.hide():r.show())},check_is_enabled:function(a){return!d(a).hasClass("disabled")},change_classes:function(a,e){var i=d(".variations-pagenav .first-page"),o=d(".variations-pagenav .prev-page"),t=d(".variations-pagenav .next-page"),n=d(".variations-pagenav .last-page");1===a?(i.addClass("disabled"),o.addClass("disabled")):(i.removeClass("disabled"),o.removeClass("disabled")),e===a?(t.addClass("disabled"),n.addClass("disabled")):(t.removeClass("disabled"),n.removeClass("disabled"))},set_page:function(a){d(".variations-pagenav .page-selector").val(a).first().change()},go_to_page:function(a,e){a=a||1,e=e||0,m.set_paginav(e),m.set_page(a)},page_selector:function(){var a=parseInt(d(this).val(),10),e=d("#variable_product_options").find(".woocommerce_variations");d(".variations-pagenav .page-selector").val(a),n.check_for_changes(),m.change_classes(a,parseInt(e.attr("data-total_pages"),10)),n.load_variations(a)},first_page:function(){return m.check_is_enabled(this)&&m.set_page(1),!1},prev_page:function(){if(m.check_is_enabled(this)){var a=d("#variable_product_options").find(".woocommerce_variations"),e=parseInt(a.attr("data-page"),10)-1,i=0<e?e:1;m.set_page(i)}return!1},next_page:function(){if(m.check_is_enabled(this)){var a=d("#variable_product_options").find(".woocommerce_variations"),e=parseInt(a.attr("data-total_pages"),10),i=parseInt(a.attr("data-page"),10)+1,o=i<=e?i:e;m.set_page(o)}return!1},last_page:function(){if(m.check_is_enabled(this)){var a=d("#variable_product_options").find(".woocommerce_variations").attr("data-total_pages");m.set_page(a)}return!1}};o.init(),t.init(),n.init(),m.init()});