jQuery(function(u){var p={states:null,init:function(){"undefined"!=typeof woocommerce_admin_meta_boxes_order&&"undefined"!=typeof woocommerce_admin_meta_boxes_order.countries&&(this.states=u.parseJSON(woocommerce_admin_meta_boxes_order.countries.replace(/&quot;/g,'"'))),u(".js_field-country").selectWoo().change(this.change_country),u(".js_field-country").trigger("change",[!0]),u(document.body).on("change","select.js_field-state",this.change_state),u("#woocommerce-order-actions input, #woocommerce-order-actions a").click(function(){window.onbeforeunload=""}),u("a.edit_address").click(this.edit_address),u("a.billing-same-as-shipping").on("click",this.copy_billing_to_shipping),u("a.load_customer_billing").on("click",this.load_billing),u("a.load_customer_shipping").on("click",this.load_shipping),u("#customer_user").on("change",this.change_customer_user)},change_country:function(e,o){if(void 0===o&&(o=!1),null!==p.states){var t,a=u(this),i=a.val(),n=a.parents("div.edit_address").find(":input.js_field-state"),r=n.parent(),c=n.attr("name"),d=n.attr("id"),m=a.data("woocommerce.stickState-"+i)?a.data("woocommerce.stickState-"+i):n.val(),_=n.attr("placeholder");if(o&&a.data("woocommerce.stickState-"+i,m),r.show().find(".select2-container").remove(),u.isEmptyObject(p.states[i]))t=u('<input type="text" />').prop("id",d).prop("name",c).prop("placeholder",_).addClass("js_field-state").val(""),n.replaceWith(t);else{var s=p.states[i],l=u('<option value=""></option>').text(woocommerce_admin_meta_boxes_order.i18n_select_state_text);t=u("<select></select>").prop("id",d).prop("name",c).prop("placeholder",_).addClass("js_field-state select short").append(l),u.each(s,function(e){var o=u("<option></option>").prop("value",e).text(s[e]);t.append(o)}),t.val(m),n.replaceWith(t),t.show().selectWoo().hide().change()}u(document.body).trigger("contry-change.woocommerce",[i,u(this).closest("div")]),u(document.body).trigger("country-change.woocommerce",[i,u(this).closest("div")])}},change_state:function(){var e=u(this),o=e.val(),t=e.parents("div.edit_address").find(":input.js_field-country"),a=t.val();t.data("woocommerce.stickState-"+a,o)},init_tiptip:function(){u("#tiptip_holder").removeAttr("style"),u("#tiptip_arrow").removeAttr("style"),u(".tips").tipTip({attribute:"data-tip",fadeIn:50,fadeOut:50,delay:200})},edit_address:function(e){e.preventDefault();var o=u(this),t=o.closest(".order_data_column"),a=t.find("div.edit_address"),i=t.find("div.address"),n=a.find(".js_field-country"),r=a.find(".js_field-state");i.hide(),o.parent().find("a").toggle(),n.val()||(n.val(woocommerce_admin_meta_boxes_order.default_country).change(),r.val(woocommerce_admin_meta_boxes_order.default_state).change()),a.show()},change_customer_user:function(){u("#_billing_country").val()||(u("a.edit_address").click(),p.load_billing(!0),p.load_shipping(!0))},load_billing:function(e){if(!0===e||window.confirm(woocommerce_admin_meta_boxes.load_billing)){var o=u("#customer_user").val();if(!o)return window.alert(woocommerce_admin_meta_boxes.no_customer_selected),!1;var t={user_id:o,action:"woocommerce_get_customer_details",security:woocommerce_admin_meta_boxes.get_customer_details_nonce};u(this).closest("div.edit_address").block({message:null,overlayCSS:{background:"#fff",opacity:.6}}),u.ajax({url:woocommerce_admin_meta_boxes.ajax_url,data:t,type:"POST",success:function(e){e&&e.billing&&u.each(e.billing,function(e,o){u(":input#_billing_"+e).val(o).change()}),u("div.edit_address").unblock()}})}return!1},load_shipping:function(e){if(!0===e||window.confirm(woocommerce_admin_meta_boxes.load_shipping)){var o=u("#customer_user").val();if(!o)return window.alert(woocommerce_admin_meta_boxes.no_customer_selected),!1;var t={user_id:o,action:"woocommerce_get_customer_details",security:woocommerce_admin_meta_boxes.get_customer_details_nonce};u(this).closest("div.edit_address").block({message:null,overlayCSS:{background:"#fff",opacity:.6}}),u.ajax({url:woocommerce_admin_meta_boxes.ajax_url,data:t,type:"POST",success:function(e){e&&e.billing&&u.each(e.shipping,function(e,o){u(":input#_shipping_"+e).val(o).change()}),u("div.edit_address").unblock()}})}return!1},copy_billing_to_shipping:function(){return window.confirm(woocommerce_admin_meta_boxes.copy_billing)&&u('.order_data_column :input[name^="_billing_"]').each(function(){var e=u(this).attr("name");e=e.replace("_billing_","_shipping_"),u(":input#"+e).val(u(this).val()).change()}),!1}},c={init:function(){this.stupidtable.init(),u("#woocommerce-order-items").on("click","button.add-line-item",this.add_line_item).on("click","button.add-coupon",this.add_coupon).on("click","a.remove-coupon",this.remove_coupon).on("click","button.refund-items",this.refund_items).on("click",".cancel-action",this.cancel).on("click","button.add-order-item",this.add_item).on("click","button.add-order-fee",this.add_fee).on("click","button.add-order-shipping",this.add_shipping).on("click","button.add-order-tax",this.add_tax).on("click","button.save-action",this.save_line_items).on("click","a.delete-order-tax",this.delete_tax).on("click","button.calculate-action",this.recalculate).on("click","a.edit-order-item",this.edit_item).on("click","a.delete-order-item",this.delete_item).on("click",".delete_refund",this.refunds.delete_refund).on("click","button.do-api-refund, button.do-manual-refund",this.refunds.do_refund).on("change",".refund input.refund_line_total, .refund input.refund_line_tax",this.refunds.input_changed).on("change keyup",".wc-order-refund-items #refund_amount",this.refunds.amount_changed).on("change","input.refund_order_item_qty",this.refunds.refund_quantity_changed).on("change","input.quantity",this.quantity_changed).on("keyup change",".split-input :input",function(){var e=u(this).parent().prev().find(":input");e&&(""===e.val()||e.is(".match-total"))&&e.val(u(this).val()).addClass("match-total")}).on("keyup",".split-input :input",function(){u(this).removeClass("match-total")}).on("click","button.add_order_item_meta",this.item_meta.add).on("click","button.remove_order_item_meta",this.item_meta.remove).on("wc_order_items_reload",this.reload_items).on("wc_order_items_reloaded",this.reloaded_items),u(document.body).on("wc_backbone_modal_loaded",this.backbone.init).on("wc_backbone_modal_response",this.backbone.response)},block:function(){u("#woocommerce-order-items").block({message:null,overlayCSS:{background:"#fff",opacity:.6}})},unblock:function(){u("#woocommerce-order-items").unblock()},reload_items:function(){var e={order_id:woocommerce_admin_meta_boxes.post_id,action:"woocommerce_load_order_items",security:woocommerce_admin_meta_boxes.order_item_nonce};c.block(),u.ajax({url:woocommerce_admin_meta_boxes.ajax_url,data:e,type:"POST",success:function(e){u("#woocommerce-order-items").find(".inside").empty(),u("#woocommerce-order-items").find(".inside").append(e),c.reloaded_items(),c.unblock()}})},reloaded_items:function(){p.init_tiptip(),c.stupidtable.init()},quantity_changed:function(){var n=u(this).closest("tr.item"),r=u(this).val(),c=u(this).attr("data-qty"),e=u("input.line_total",n),o=u("input.line_subtotal",n),t=accounting.unformat(e.attr("data-total"),woocommerce_admin.mon_decimal_point)/c;e.val(parseFloat(accounting.formatNumber(t*r,woocommerce_admin_meta_boxes.rounding_precision,"")).toString().replace(".",woocommerce_admin.mon_decimal_point));var a=accounting.unformat(o.attr("data-subtotal"),woocommerce_admin.mon_decimal_point)/c;o.val(parseFloat(accounting.formatNumber(a*r,woocommerce_admin_meta_boxes.rounding_precision,"")).toString().replace(".",woocommerce_admin.mon_decimal_point)),u("input.line_tax",n).each(function(){var e=u(this),o=e.data("tax_id"),t=accounting.unformat(e.attr("data-total_tax"),woocommerce_admin.mon_decimal_point)/c,a=u('input.line_subtotal_tax[data-tax_id="'+o+'"]',n),i=accounting.unformat(a.attr("data-subtotal_tax"),woocommerce_admin.mon_decimal_point)/c;0<t&&e.val(parseFloat(accounting.formatNumber(t*r,woocommerce_admin_meta_boxes.rounding_precision,"")).toString().replace(".",woocommerce_admin.mon_decimal_point)),0<i&&a.val(parseFloat(accounting.formatNumber(i*r,woocommerce_admin_meta_boxes.rounding_precision,"")).toString().replace(".",woocommerce_admin.mon_decimal_point))}),u(this).trigger("quantity_changed")},add_line_item:function(){return u("div.wc-order-add-item").slideDown(),u("div.wc-order-data-row-toggle").not("div.wc-order-add-item").slideUp(),!1},add_coupon:function(){var e=window.prompt(woocommerce_admin_meta_boxes.i18n_apply_coupon);if(null!=e){c.block();var o=u("#customer_user").val(),t=u("#_billing_email").val(),a=u.extend({},c.get_taxable_address(),{action:"woocommerce_add_coupon_discount",dataType:"json",order_id:woocommerce_admin_meta_boxes.post_id,security:woocommerce_admin_meta_boxes.order_item_nonce,coupon:e,user_id:o,user_email:t});u.post(woocommerce_admin_meta_boxes.ajax_url,a,function(e){e.success?(u("#woocommerce-order-items").find(".inside").empty(),u("#woocommerce-order-items").find(".inside").append(e.data.html),c.reloaded_items(),c.unblock()):window.alert(e.data.error),c.unblock()})}return!1},remove_coupon:function(){var e=u(this);c.block();var o=u.extend({},c.get_taxable_address(),{action:"woocommerce_remove_order_coupon",dataType:"json",order_id:woocommerce_admin_meta_boxes.post_id,security:woocommerce_admin_meta_boxes.order_item_nonce,coupon:e.data("code")});u.post(woocommerce_admin_meta_boxes.ajax_url,o,function(e){e.success?(u("#woocommerce-order-items").find(".inside").empty(),u("#woocommerce-order-items").find(".inside").append(e.data.html),c.reloaded_items(),c.unblock()):window.alert(e.data.error),c.unblock()})},refund_items:function(){return u("div.wc-order-refund-items").slideDown(),u("div.wc-order-data-row-toggle").not("div.wc-order-refund-items").slideUp(),u("div.wc-order-totals-items").slideUp(),u("#woocommerce-order-items").find("div.refund").show(),u(".wc-order-edit-line-item .wc-order-edit-line-item-actions").hide(),!1},cancel:function(){return u("div.wc-order-data-row-toggle").not("div.wc-order-bulk-actions").slideUp(),u("div.wc-order-bulk-actions").slideDown(),u("div.wc-order-totals-items").slideDown(),u("#woocommerce-order-items").find("div.refund").hide(),u(".wc-order-edit-line-item .wc-order-edit-line-item-actions").show(),"true"===u(this).attr("data-reload")&&c.reload_items(),!1},add_item:function(){return u(this).WCBackboneModal({template:"wc-modal-add-products"}),!1},add_fee:function(){var e=window.prompt(woocommerce_admin_meta_boxes.i18n_add_fee);if(null!=e){c.block();var o=u.extend({},c.get_taxable_address(),{action:"woocommerce_add_order_fee",dataType:"json",order_id:woocommerce_admin_meta_boxes.post_id,security:woocommerce_admin_meta_boxes.order_item_nonce,amount:e});u.post(woocommerce_admin_meta_boxes.ajax_url,o,function(e){e.success?(u("#woocommerce-order-items").find(".inside").empty(),u("#woocommerce-order-items").find(".inside").append(e.data.html),c.reloaded_items(),c.unblock()):window.alert(e.data.error),c.unblock()})}return!1},add_shipping:function(){c.block();var e={action:"woocommerce_add_order_shipping",order_id:woocommerce_admin_meta_boxes.post_id,security:woocommerce_admin_meta_boxes.order_item_nonce,dataType:"json"};return u.post(woocommerce_admin_meta_boxes.ajax_url,e,function(e){e.success?u("table.woocommerce_order_items tbody#order_shipping_line_items").append(e.data.html):window.alert(e.data.error),c.unblock()}),!1},add_tax:function(){return u(this).WCBackboneModal({template:"wc-modal-add-tax"}),!1},edit_item:function(){return u(this).closest("tr").find(".view").hide(),u(this).closest("tr").find(".edit").show(),u(this).hide(),u("button.add-line-item").click(),u("button.cancel-action").attr("data-reload",!0),!1},delete_item:function(){if(window.confirm(woocommerce_admin_meta_boxes.remove_item_notice)){var e=u(this).closest("tr.item, tr.fee, tr.shipping").attr("data-order_item_id");c.block();var o=u.extend({},c.get_taxable_address(),{order_id:woocommerce_admin_meta_boxes.post_id,order_item_ids:e,action:"woocommerce_remove_order_item",security:woocommerce_admin_meta_boxes.order_item_nonce});"true"===u("button.cancel-action").attr("data-reload")&&(o.items=u("table.woocommerce_order_items :input[name], .wc-order-totals-items :input[name]").serialize()),u.ajax({url:woocommerce_admin_meta_boxes.ajax_url,data:o,type:"POST",success:function(e){e.success?(u("#woocommerce-order-items").find(".inside").empty(),u("#woocommerce-order-items").find(".inside").append(e.data.html),e.data.notes_html&&(u("ul.order_notes").empty(),u("ul.order_notes").append(u(e.data.notes_html).find("li"))),c.reloaded_items(),c.unblock()):window.alert(e.data.error),c.unblock()}})}return!1},delete_tax:function(){if(window.confirm(woocommerce_admin_meta_boxes.i18n_delete_tax)){c.block();var e={action:"woocommerce_remove_order_tax",rate_id:u(this).attr("data-rate_id"),order_id:woocommerce_admin_meta_boxes.post_id,security:woocommerce_admin_meta_boxes.order_item_nonce};u.ajax({url:woocommerce_admin_meta_boxes.ajax_url,data:e,type:"POST",success:function(e){e.success?(u("#woocommerce-order-items").find(".inside").empty(),u("#woocommerce-order-items").find(".inside").append(e.data.html),c.reloaded_items(),c.unblock()):window.alert(e.data.error),c.unblock()}})}return!1},get_taxable_address:function(){var e="",o="",t="",a="";return"shipping"===woocommerce_admin_meta_boxes.tax_based_on&&(e=u("#_shipping_country").val(),o=u("#_shipping_state").val(),t=u("#_shipping_postcode").val(),a=u("#_shipping_city").val()),"billing"!==woocommerce_admin_meta_boxes.tax_based_on&&e||(e=u("#_billing_country").val(),o=u("#_billing_state").val(),t=u("#_billing_postcode").val(),a=u("#_billing_city").val()),{country:e,state:o,postcode:t,city:a}},recalculate:function(){if(window.confirm(woocommerce_admin_meta_boxes.calc_totals)){c.block();var e=u.extend({},c.get_taxable_address(),{action:"woocommerce_calc_line_taxes",order_id:woocommerce_admin_meta_boxes.post_id,items:u("table.woocommerce_order_items :input[name], .wc-order-totals-items :input[name]").serialize(),security:woocommerce_admin_meta_boxes.calc_totals_nonce});u(document.body).trigger("order-totals-recalculate-before",e),u.ajax({url:woocommerce_admin_meta_boxes.ajax_url,data:e,type:"POST",success:function(e){u("#woocommerce-order-items").find(".inside").empty(),u("#woocommerce-order-items").find(".inside").append(e),c.reloaded_items(),c.unblock(),u(document.body).trigger("order-totals-recalculate-success",e)},complete:function(e){u(document.body).trigger("order-totals-recalculate-complete",e)}})}return!1},save_line_items:function(){var e={order_id:woocommerce_admin_meta_boxes.post_id,items:u("table.woocommerce_order_items :input[name], .wc-order-totals-items :input[name]").serialize(),action:"woocommerce_save_order_items",security:woocommerce_admin_meta_boxes.order_item_nonce};return c.block(),u.ajax({url:woocommerce_admin_meta_boxes.ajax_url,data:e,type:"POST",success:function(e){e.success?(u("#woocommerce-order-items").find(".inside").empty(),u("#woocommerce-order-items").find(".inside").append(e.data.html),e.data.notes_html&&(u("ul.order_notes").empty(),u("ul.order_notes").append(u(e.data.notes_html).find("li"))),c.reloaded_items(),c.unblock()):(c.unblock(),window.alert(e.data.error))}}),u(this).trigger("items_saved"),!1},refunds:{do_refund:function(){if(c.block(),window.confirm(woocommerce_admin_meta_boxes.i18n_do_refund)){var e=u("input#refund_amount").val(),o=u("input#refund_reason").val(),t=u("input#refunded_amount").val(),a={},i={},n={};u(".refund input.refund_order_item_qty").each(function(e,o){u(o).closest("tr").data("order_item_id")&&o.value&&(a[u(o).closest("tr").data("order_item_id")]=o.value)}),u(".refund input.refund_line_total").each(function(e,o){u(o).closest("tr").data("order_item_id")&&(i[u(o).closest("tr").data("order_item_id")]=accounting.unformat(o.value,woocommerce_admin.mon_decimal_point))}),u(".refund input.refund_line_tax").each(function(e,o){if(u(o).closest("tr").data("order_item_id")){var t=u(o).data("tax_id");n[u(o).closest("tr").data("order_item_id")]||(n[u(o).closest("tr").data("order_item_id")]={}),n[u(o).closest("tr").data("order_item_id")][t]=accounting.unformat(o.value,woocommerce_admin.mon_decimal_point)}});var r={action:"woocommerce_refund_line_items",order_id:woocommerce_admin_meta_boxes.post_id,refund_amount:e,refunded_amount:t,refund_reason:o,line_item_qtys:JSON.stringify(a,null,""),line_item_totals:JSON.stringify(i,null,""),line_item_tax_totals:JSON.stringify(n,null,""),api_refund:u(this).is(".do-api-refund"),restock_refunded_items:u("#restock_refunded_items:checked").length?"true":"false",security:woocommerce_admin_meta_boxes.order_item_nonce};u.post(woocommerce_admin_meta_boxes.ajax_url,r,function(e){!0===e.success?window.location.reload():(window.alert(e.data.error),c.reload_items(),c.unblock())})}else c.unblock()},delete_refund:function(){if(window.confirm(woocommerce_admin_meta_boxes.i18n_delete_refund)){var e=u(this).closest("tr.refund").attr("data-order_refund_id");c.block();var o={action:"woocommerce_delete_refund",refund_id:e,security:woocommerce_admin_meta_boxes.order_item_nonce};u.ajax({url:woocommerce_admin_meta_boxes.ajax_url,data:o,type:"POST",success:function(){c.reload_items()}})}return!1},input_changed:function(){var t=0;u(".woocommerce_order_items").find("tr.item, tr.fee, tr.shipping").each(function(){u(this).find(".refund input:not(.refund_order_item_qty)").each(function(e,o){t+=parseFloat(accounting.unformat(u(o).val()||0,woocommerce_admin.mon_decimal_point))})}),u("#refund_amount").val(accounting.formatNumber(t,woocommerce_admin_meta_boxes.currency_format_num_decimals,"",woocommerce_admin.mon_decimal_point)).change()},amount_changed:function(){var e=accounting.unformat(u(this).val(),woocommerce_admin.mon_decimal_point);u("button .wc-order-refund-amount .amount").text(accounting.formatMoney(e,{symbol:woocommerce_admin_meta_boxes.currency_format_symbol,decimal:woocommerce_admin_meta_boxes.currency_format_decimal_sep,thousand:woocommerce_admin_meta_boxes.currency_format_thousand_sep,precision:woocommerce_admin_meta_boxes.currency_format_num_decimals,format:woocommerce_admin_meta_boxes.currency_format}))},refund_quantity_changed:function(){var i=u(this).closest("tr.item"),n=i.find("input.quantity").val(),r=u(this).val(),e=u("input.line_total",i),o=u("input.refund_line_total",i),t=accounting.unformat(e.attr("data-total"),woocommerce_admin.mon_decimal_point)/n;o.val(parseFloat(accounting.formatNumber(t*r,woocommerce_admin_meta_boxes.rounding_precision,"")).toString().replace(".",woocommerce_admin.mon_decimal_point)).change(),u(".refund_line_tax",i).each(function(){var e=u(this),o=e.data("tax_id"),t=u('input.line_tax[data-tax_id="'+o+'"]',i),a=accounting.unformat(t.data("total_tax"),woocommerce_admin.mon_decimal_point)/n;0<a?e.val(parseFloat(accounting.formatNumber(a*r,woocommerce_admin_meta_boxes.rounding_precision,"")).toString().replace(".",woocommerce_admin.mon_decimal_point)).change():e.val(0).change()}),0<r?u("#restock_refunded_items").closest("tr").show():(u("#restock_refunded_items").closest("tr").hide(),u(".woocommerce_order_items input.refund_order_item_qty").each(function(){0<u(this).val()&&u("#restock_refunded_items").closest("tr").show()})),u(this).trigger("refund_quantity_changed")}},item_meta:{add:function(){var e=u(this).closest("tr.item, tr.shipping"),o=e.find("tbody.meta_items"),t=o.find("tr").length+1,a='<tr data-meta_id="0"><td><input type="text" maxlength="255" placeholder="'+woocommerce_admin_meta_boxes_order.placeholder_name+'" name="meta_key['+e.attr("data-order_item_id")+"][new-"+t+']" /><textarea placeholder="'+woocommerce_admin_meta_boxes_order.placeholder_value+'" name="meta_value['+e.attr("data-order_item_id")+"][new-"+t+']"></textarea></td><td width="1%"><button class="remove_order_item_meta button">&times;</button></td></tr>';return o.append(a),!1},remove:function(){if(window.confirm(woocommerce_admin_meta_boxes.remove_item_meta)){var e=u(this).closest("tr");e.find(":input").val(""),e.hide()}return!1}},backbone:{init:function(e,o){"wc-modal-add-products"===o&&(u(document.body).trigger("wc-enhanced-select-init"),u(this).on("change",".wc-product-search",function(){if(u(this).closest("tr").is(":last-child")){var e=u(this).closest("table.widefat").find("tbody"),o=e.find("tr").length,t=e.data("row").replace(/\[0\]/g,"["+o+"]");e.append("<tr>"+t+"</tr>"),u(document.body).trigger("wc-enhanced-select-init")}}))},response:function(e,o,t){if("wc-modal-add-tax"===o){var a=t.add_order_tax,i="";t.manual_tax_rate_id&&(i=t.manual_tax_rate_id),c.backbone.add_tax(a,i)}if("wc-modal-add-products"===o){var n=u(this).find("table.widefat").find("tbody").find("tr"),r=[];return u(n).each(function(){var e=u(this).find(':input[name="item_id"]').val(),o=u(this).find(':input[name="item_qty"]').val();r.push({id:e,qty:o||1})}),c.backbone.add_items(r)}},add_items:function(e){c.block();var o={action:"woocommerce_add_order_item",order_id:woocommerce_admin_meta_boxes.post_id,security:woocommerce_admin_meta_boxes.order_item_nonce,data:e};"true"===u("button.cancel-action").attr("data-reload")&&(o.items=u("table.woocommerce_order_items :input[name], .wc-order-totals-items :input[name]").serialize()),u.ajax({type:"POST",url:woocommerce_admin_meta_boxes.ajax_url,data:o,success:function(e){e.success?(u("#woocommerce-order-items").find(".inside").empty(),u("#woocommerce-order-items").find(".inside").append(e.data.html),e.data.notes_html&&(u("ul.order_notes").empty(),u("ul.order_notes").append(u(e.data.notes_html).find("li"))),c.reloaded_items(),c.unblock()):(c.unblock(),window.alert(e.data.error))},dataType:"json"})},add_tax:function(e,o){if(o&&(e=o),!e)return!1;var t=u(".order-tax-id").map(function(){return u(this).val()}).get();if(-1===u.inArray(e,t)){c.block();var a={action:"woocommerce_add_order_tax",rate_id:e,order_id:woocommerce_admin_meta_boxes.post_id,security:woocommerce_admin_meta_boxes.order_item_nonce};u.ajax({url:woocommerce_admin_meta_boxes.ajax_url,data:a,dataType:"json",type:"POST",success:function(e){e.success?(u("#woocommerce-order-items").find(".inside").empty(),u("#woocommerce-order-items").find(".inside").append(e.data.html),c.reloaded_items()):window.alert(e.data.error),c.unblock()}})}else window.alert(woocommerce_admin_meta_boxes.i18n_tax_rate_already_exists)}},stupidtable:{init:function(){u(".woocommerce_order_items").stupidtable(),u(".woocommerce_order_items").on("aftertablesort",this.add_arrows)},add_arrows:function(e,o){var t=u(this).find("th"),a="asc"===o.direction?"&uarr;":"&darr;",i=o.column;t.find(".wc-arrow").remove(),t.eq(i).append('<span class="wc-arrow">'+a+"</span>")}}},e={init:function(){u("#woocommerce-order-notes").on("click","button.add_note",this.add_order_note).on("click","a.delete_note",this.delete_order_note)},add_order_note:function(){if(u("textarea#add_order_note").val()){u("#woocommerce-order-notes").block({message:null,overlayCSS:{background:"#fff",opacity:.6}});var e={action:"woocommerce_add_order_note",post_id:woocommerce_admin_meta_boxes.post_id,note:u("textarea#add_order_note").val(),note_type:u("select#order_note_type").val(),security:woocommerce_admin_meta_boxes.add_order_note_nonce};return u.post(woocommerce_admin_meta_boxes.ajax_url,e,function(e){u("ul.order_notes").prepend(e),u("#woocommerce-order-notes").unblock(),u("#add_order_note").val("")}),!1}},delete_order_note:function(){if(window.confirm(woocommerce_admin_meta_boxes.i18n_delete_note)){var e=u(this).closest("li.note");u(e).block({message:null,overlayCSS:{background:"#fff",opacity:.6}});var o={action:"woocommerce_delete_order_note",note_id:u(e).attr("rel"),security:woocommerce_admin_meta_boxes.delete_order_note_nonce};u.post(woocommerce_admin_meta_boxes.ajax_url,o,function(){u(e).remove()})}return!1}},o={init:function(){u(".order_download_permissions").on("click","button.grant_access",this.grant_access).on("click","button.revoke_access",this.revoke_access).on("click","#copy-download-link",this.copy_link).on("aftercopy","#copy-download-link",this.copy_success).on("aftercopyfailure","#copy-download-link",this.copy_fail)},grant_access:function(){var e=u("#grant_access_id").val();if(e){u(".order_download_permissions").block({message:null,overlayCSS:{background:"#fff",opacity:.6}});var o={action:"woocommerce_grant_access_to_download",product_ids:e,loop:u(".order_download_permissions .wc-metabox").length,order_id:woocommerce_admin_meta_boxes.post_id,security:woocommerce_admin_meta_boxes.grant_access_nonce};return u.post(woocommerce_admin_meta_boxes.ajax_url,o,function(e){e?u(".order_download_permissions .wc-metaboxes").append(e):window.alert(woocommerce_admin_meta_boxes.i18n_download_permission_fail),u(document.body).trigger("wc-init-datepickers"),u("#grant_access_id").val("").change(),u(".order_download_permissions").unblock()}),!1}},revoke_access:function(){if(window.confirm(woocommerce_admin_meta_boxes.i18n_permission_revoke)){var e=u(this).parent().parent(),o=u(this).attr("rel").split(",")[0],t=u(this).attr("rel").split(",")[1],a=u(this).data("permission_id");if(0<o){u(e).block({message:null,overlayCSS:{background:"#fff",opacity:.6}});var i={action:"woocommerce_revoke_access_to_download",product_id:o,download_id:t,permission_id:a,order_id:woocommerce_admin_meta_boxes.post_id,security:woocommerce_admin_meta_boxes.revoke_access_nonce};u.post(woocommerce_admin_meta_boxes.ajax_url,i,function(){u(e).fadeOut("300",function(){u(e).remove()})})}else u(e).fadeOut("300",function(){u(e).remove()})}return!1},copy_link:function(e){wcClearClipboard(),wcSetClipboard(u(this).attr("href"),u(this)),e.preventDefault()},copy_success:function(){u(this).tipTip({attribute:"data-tip",activation:"focus",fadeIn:50,fadeOut:50,delay:0}).focus()},copy_fail:function(){u(this).tipTip({attribute:"data-tip-failed",activation:"focus",fadeIn:50,fadeOut:50,delay:0}).focus()}};p.init(),c.init(),e.init(),o.init()});