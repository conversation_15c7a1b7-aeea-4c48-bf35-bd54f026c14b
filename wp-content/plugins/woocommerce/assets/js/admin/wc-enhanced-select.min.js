jQuery(function(a){function c(){return{language:{errorLoading:function(){return wc_enhanced_select_params.i18n_searching},inputTooLong:function(e){var t=e.input.length-e.maximum;return 1==t?wc_enhanced_select_params.i18n_input_too_long_1:wc_enhanced_select_params.i18n_input_too_long_n.replace("%qty%",t)},inputTooShort:function(e){var t=e.minimum-e.input.length;return 1==t?wc_enhanced_select_params.i18n_input_too_short_1:wc_enhanced_select_params.i18n_input_too_short_n.replace("%qty%",t)},loadingMore:function(){return wc_enhanced_select_params.i18n_load_more},maximumSelected:function(e){return 1===e.maximum?wc_enhanced_select_params.i18n_selection_too_long_1:wc_enhanced_select_params.i18n_selection_too_long_n.replace("%qty%",e.maximum)},noResults:function(){return wc_enhanced_select_params.i18n_no_matches},searching:function(){return wc_enhanced_select_params.i18n_searching}}}}try{a(document.body).on("wc-enhanced-select-init",function(){a(":input.wc-enhanced-select, :input.chosen_select").filter(":not(.enhanced)").each(function(){var e=a.extend({minimumResultsForSearch:10,allowClear:!!a(this).data("allow_clear"),placeholder:a(this).data("placeholder")},c());a(this).selectWoo(e).addClass("enhanced")}),a(":input.wc-enhanced-select-nostd, :input.chosen_select_nostd").filter(":not(.enhanced)").each(function(){var e=a.extend({minimumResultsForSearch:10,allowClear:!0,placeholder:a(this).data("placeholder")},c());a(this).selectWoo(e).addClass("enhanced")}),a(":input.wc-product-search").filter(":not(.enhanced)").each(function(){var e={allowClear:!!a(this).data("allow_clear"),placeholder:a(this).data("placeholder"),minimumInputLength:a(this).data("minimum_input_length")?a(this).data("minimum_input_length"):"3",escapeMarkup:function(e){return e},ajax:{url:wc_enhanced_select_params.ajax_url,dataType:"json",delay:250,data:function(e){return{term:e.term,action:a(this).data("action")||"woocommerce_json_search_products_and_variations",security:wc_enhanced_select_params.search_products_nonce,exclude:a(this).data("exclude"),include:a(this).data("include"),limit:a(this).data("limit"),display_stock:a(this).data("display_stock")}},processResults:function(e){var n=[];return e&&a.each(e,function(e,t){n.push({id:e,text:t})}),{results:n}},cache:!0}};if(e=a.extend(e,c()),a(this).selectWoo(e).addClass("enhanced"),a(this).data("sortable")){var n=a(this),t=a(this).next(".select2-container").find("ul.select2-selection__rendered");t.sortable({placeholder:"ui-state-highlight select2-selection__choice",forcePlaceholderSize:!0,items:"li:not(.select2-search__field)",tolerance:"pointer",stop:function(){a(t.find(".select2-selection__choice").get().reverse()).each(function(){var e=a(this).data("data").id,t=n.find('option[value="'+e+'"]')[0];n.prepend(t)})}})}else a(this).prop("multiple")&&a(this).on("change",function(){var e=a(this).children();e.sort(function(e,t){var n=e.text.toLowerCase(),a=t.text.toLowerCase();return a<n?1:n<a?-1:0}),a(this).html(e)})}),a(":input.wc-customer-search").filter(":not(.enhanced)").each(function(){var e={allowClear:!!a(this).data("allow_clear"),placeholder:a(this).data("placeholder"),minimumInputLength:a(this).data("minimum_input_length")?a(this).data("minimum_input_length"):"1",escapeMarkup:function(e){return e},ajax:{url:wc_enhanced_select_params.ajax_url,dataType:"json",delay:1e3,data:function(e){return{term:e.term,action:"woocommerce_json_search_customers",security:wc_enhanced_select_params.search_customers_nonce,exclude:a(this).data("exclude")}},processResults:function(e){var n=[];return e&&a.each(e,function(e,t){n.push({id:e,text:t})}),{results:n}},cache:!0}};if(e=a.extend(e,c()),a(this).selectWoo(e).addClass("enhanced"),a(this).data("sortable")){var n=a(this),t=a(this).next(".select2-container").find("ul.select2-selection__rendered");t.sortable({placeholder:"ui-state-highlight select2-selection__choice",forcePlaceholderSize:!0,items:"li:not(.select2-search__field)",tolerance:"pointer",stop:function(){a(t.find(".select2-selection__choice").get().reverse()).each(function(){var e=a(this).data("data").id,t=n.find('option[value="'+e+'"]')[0];n.prepend(t)})}})}}),a(":input.wc-category-search").filter(":not(.enhanced)").each(function(){var e=a.extend({allowClear:!!a(this).data("allow_clear"),placeholder:a(this).data("placeholder"),minimumInputLength:a(this).data("minimum_input_length")?a(this).data("minimum_input_length"):3,escapeMarkup:function(e){return e},ajax:{url:wc_enhanced_select_params.ajax_url,dataType:"json",delay:250,data:function(e){return{term:e.term,action:"woocommerce_json_search_categories",security:wc_enhanced_select_params.search_categories_nonce}},processResults:function(e){var n=[];return e&&a.each(e,function(e,t){n.push({id:t.slug,text:t.formatted_name})}),{results:n}},cache:!0}},c());a(this).selectWoo(e).addClass("enhanced")})}).on("wc_backbone_modal_before_remove",function(){a(".wc-enhanced-select, :input.wc-product-search, :input.wc-customer-search").filter(".select2-hidden-accessible").selectWoo("close")}).trigger("wc-enhanced-select-init"),a("html").on("click",function(e){this===e.target&&a(".wc-enhanced-select, :input.wc-product-search, :input.wc-customer-search").filter(".select2-hidden-accessible").selectWoo("close")})}catch(e){window.console.log(e)}});