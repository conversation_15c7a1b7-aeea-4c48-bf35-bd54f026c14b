jQuery(function(q){q("#the-list").on("click",".editinline",function(){inlineEditPost.revert();var e=q(this).closest("tr").attr("id");e=e.replace("post-","");var t=q("#woocommerce_inline_"+e),i=t.find(".sku").text(),n=t.find(".regular_price").text(),o=t.find(".sale_price ").text(),d=t.find(".weight").text(),s=t.find(".length").text(),l=t.find(".width").text(),c=t.find(".height").text(),a=t.find(".shipping_class").text(),r=t.find(".visibility").text(),_=t.find(".stock_status").text(),m=t.find(".stock").text(),p=t.find(".featured").text(),f=t.find(".manage_stock").text(),u=t.find(".menu_order").text(),h=t.find(".tax_status").text(),w=t.find(".tax_class").text(),k=t.find(".backorders").text(),v=n.replace(".",woocommerce_admin.mon_decimal_point),x=o.replace(".",woocommerce_admin.mon_decimal_point);q('input[name="_sku"]',".inline-edit-row").val(i),q('input[name="_regular_price"]',".inline-edit-row").val(v),q('input[name="_sale_price"]',".inline-edit-row").val(x),q('input[name="_weight"]',".inline-edit-row").val(d),q('input[name="_length"]',".inline-edit-row").val(s),q('input[name="_width"]',".inline-edit-row").val(l),q('input[name="_height"]',".inline-edit-row").val(c),q('select[name="_shipping_class"] option:selected',".inline-edit-row").attr("selected",!1).change(),q('select[name="_shipping_class"] option[value="'+a+'"]').attr("selected","selected").change(),q('input[name="_stock"]',".inline-edit-row").val(m),q('input[name="menu_order"]',".inline-edit-row").val(u),q('select[name="_tax_status"] option, select[name="_tax_class"] option, select[name="_visibility"] option, select[name="_stock_status"] option, select[name="_backorders"] option').removeAttr("selected"),q('select[name="_tax_status"] option[value="'+h+'"]',".inline-edit-row").attr("selected","selected"),q('select[name="_tax_class"] option[value="'+w+'"]',".inline-edit-row").attr("selected","selected"),q('select[name="_visibility"] option[value="'+r+'"]',".inline-edit-row").attr("selected","selected"),q('select[name="_stock_status"] option[value="'+_+'"]',".inline-edit-row").attr("selected","selected"),q('select[name="_backorders"] option[value="'+k+'"]',".inline-edit-row").attr("selected","selected"),"yes"===p?q('input[name="_featured"]',".inline-edit-row").attr("checked","checked"):q('input[name="_featured"]',".inline-edit-row").removeAttr("checked");var g=t.find(".product_type").text(),y=t.find(".product_is_virtual").text(),b="external"!==g,A="external"!==g&&"grouped"!==g;q(".stock_fields, .manage_stock_field, .stock_status_field, .backorder_field").show(),A?"yes"===f?(q(".stock_qty_field, .backorder_field",".inline-edit-row").show().removeAttr("style"),q(".stock_status_field").hide(),q(".manage_stock_field input").prop("checked",!0)):(q(".stock_qty_field, .backorder_field",".inline-edit-row").hide(),q(".stock_status_field").show().removeAttr("style"),q(".manage_stock_field input").prop("checked",!1)):b?q(".stock_fields, .manage_stock_field, .backorder_field").hide():q(".stock_fields, .manage_stock_field, .stock_status_field, .backorder_field").hide(),"simple"===g||"external"===g?q(".price_fields",".inline-edit-row").show().removeAttr("style"):q(".price_fields",".inline-edit-row").hide(),"yes"===y?q(".dimension_fields",".inline-edit-row").hide():q(".dimension_fields",".inline-edit-row").show().removeAttr("style"),q('input[name="comment_status"]').parent().find(".checkbox-title").text(woocommerce_quick_edit.strings.allow_reviews)}),q("#the-list").on("change",'.inline-edit-row input[name="_manage_stock"]',function(){q(this).is(":checked")?(q(".stock_qty_field, .backorder_field",".inline-edit-row").show().removeAttr("style"),q(".stock_status_field").hide()):(q(".stock_qty_field, .backorder_field",".inline-edit-row").hide(),q(".stock_status_field").show().removeAttr("style"))}),q("#wpbody").on("click","#doaction, #doaction2",function(){q("input.text",".inline-edit-row").val(""),q("#woocommerce-fields").find("select").prop("selectedIndex",0),q("#woocommerce-fields-bulk").find(".inline-edit-group .change-input").hide()}),q("#wpbody").on("change","#woocommerce-fields-bulk .inline-edit-group .change_to",function(){0<q(this).val()?q(this).closest("div").find(".change-input").show():q(this).closest("div").find(".change-input").hide()}),q("#wpbody").on("click",".trash-product",function(){return window.confirm(woocommerce_admin.i18n_delete_product_notice)})});