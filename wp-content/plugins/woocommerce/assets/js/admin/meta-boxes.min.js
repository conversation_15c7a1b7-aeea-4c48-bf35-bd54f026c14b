jQuery(function(o){!function t(){o("#tiptip_holder").removeAttr("style"),o("#tiptip_arrow").removeAttr("style"),o(".tips").tipTip({attribute:"data-tip",fadeIn:50,fadeOut:50,delay:200})}(),o("#titlediv").find("#title").keyup(function(t){if("9"===(t.keyCode||t.which)&&0<o("#woocommerce-coupon-description").length)return t.stopPropagation(),o("#woocommerce-coupon-description").focus(),!1}),o(".wc-metaboxes-wrapper").on("click",".wc-metabox > h3",function(){o(this).parent(".wc-metabox").toggleClass("closed").toggleClass("open")}),o(document.body).on("wc-init-tabbed-panels",function(){o("ul.wc-tabs").show(),o("ul.wc-tabs a").click(function(t){t.preventDefault();var e=o(this).closest("div.panel-wrap");o("ul.wc-tabs li",e).removeClass("active"),o(this).parent().addClass("active"),o("div.panel",e).hide(),o(o(this).attr("href")).show()}),o("div.panel-wrap").each(function(){o(this).find("ul.wc-tabs li").eq(0).find("a").click()})}).trigger("wc-init-tabbed-panels"),o(document.body).on("wc-init-datepickers",function(){o(".date-picker-field, .date-picker").datepicker({dateFormat:"yy-mm-dd",numberOfMonths:1,showButtonPanel:!0})}).trigger("wc-init-datepickers"),o(".wc-metaboxes-wrapper").on("click",".wc-metabox h3",function(t){o(t.target).filter(":input, option, .sort").length||o(this).next(".wc-metabox-content").stop().slideToggle()}).on("click",".expand_all",function(){return o(this).closest(".wc-metaboxes-wrapper").find(".wc-metabox > .wc-metabox-content").show(),!1}).on("click",".close_all",function(){return o(this).closest(".wc-metaboxes-wrapper").find(".wc-metabox > .wc-metabox-content").hide(),!1}),o(".wc-metabox.closed").each(function(){o(this).find(".wc-metabox-content").hide()})});