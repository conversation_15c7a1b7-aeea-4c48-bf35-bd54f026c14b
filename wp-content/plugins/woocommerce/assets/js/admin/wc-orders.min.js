jQuery(function(r){if("undefined"==typeof wc_orders_params)return!1;function e(){r(document).on("click",".post-type-shop_order .wp-list-table tbody td",this.onRowClick).on("click",".order-preview:not(.disabled)",this.onPreview)}e.prototype.onRowClick=function(e){if(r(e.target).filter("a, a *, .no-link, .no-link *, button, button *").length)return!0;if(window.getSelection&&window.getSelection().toString().length)return!0;var t=r(this).closest("tr").find("a.order-view").attr("href");t&&t.length&&(e.preventDefault(),e.metaKey||e.ctrlKey?window.open(t,"_blank"):window.location=t)},e.prototype.onPreview=function(){var t=r(this),e=t.data("order-id");return t.data("order-data")?r(this).WCBackboneModal({template:"wc-modal-view-order",variable:t.data("order-data")}):(t.addClass("disabled"),r.ajax({url:wc_orders_params.ajax_url,data:{order_id:e,action:"woocommerce_get_order_details",security:wc_orders_params.preview_nonce},type:"GET",success:function(e){r(".order-preview").removeClass("disabled"),e.success&&(t.data("order-data",e.data),r(this).WCBackboneModal({template:"wc-modal-view-order",variable:e.data}))}})),!1},new e});