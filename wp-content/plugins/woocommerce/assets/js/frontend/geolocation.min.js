jQuery(function(e){function a(){wc_geolocation_params.hash&&e('a[href^="'+wc_geolocation_params.home_url+'"]:not(a[href*="v="]), a[href^="/"]:not(a[href*="v="])').each(function(){var a=e(this),o=a.attr("href"),t=o.split("#");o=0<(o=t[0]).indexOf("?")?o+"&v="+wc_geolocation_params.hash:o+"?v="+wc_geolocation_params.hash,"undefined"!=typeof t[1]&&null!==t[1]&&(o=o+"#"+t[1]),a.attr("href",o)})}var o=window.location.toString(),t={url:wc_geolocation_params.wc_ajax_url.toString().replace("%%endpoint%%","get_customer_location"),type:"GET",success:function(a){a.success&&a.data.hash&&a.data.hash!==wc_geolocation_params.hash&&function(a){o=0<o.indexOf("?v=")||0<o.indexOf("&v=")?o.replace(/v=[^&]+/,"v="+a):0<o.indexOf("?")?o+"&v="+a:o+"?v="+a,window.location=o}(a.data.hash)}};"1"===wc_geolocation_params.is_available&&(e.ajax(t),e("form").each(function(){var a=e(this),o=a.attr("method"),t=0<a.find('input[name="v"]').length;if(o&&"get"===o.toLowerCase()&&!t)a.append('<input type="hidden" name="v" value="'+wc_geolocation_params.hash+'" />');else{var n=a.attr("action");n&&(0<n.indexOf("?")?a.attr("action",n+"&v="+wc_geolocation_params.hash):a.attr("action",n+"?v="+wc_geolocation_params.hash))}}),a()),e(document.body).on("added_to_cart",function(){a()})});