jQuery(function(i){i(".woocommerce-ordering").on("change","select.orderby",function(){i(this).closest("form").submit()}),i("input.qty:not(.product-quantity input.qty)").each(function(){var e=parseFloat(i(this).attr("min"));0<=e&&parseFloat(i(this).val())<e&&i(this).val(e)});var o="store_notice"+(i(".woocommerce-store-notice").data("notice-id")||"");"hidden"===Cookies.get(o)?i(".woocommerce-store-notice").hide():i(".woocommerce-store-notice").show(),i(".woocommerce-store-notice__dismiss-link").click(function(e){Cookies.set(o,"hidden",{path:"/"}),i(".woocommerce-store-notice").hide(),e.preventDefault()}),i(document.body).on("click",function(){i(".woocommerce-input-wrapper span.description:visible").prop("aria-hidden",!0).slideUp(250)}),i(".woocommerce-input-wrapper").on("click",function(e){e.stopPropagation()}),i(".woocommerce-input-wrapper :input").on("keydown",function(e){var o=i(this).parent().find("span.description");if(27===e.which&&o.length&&o.is(":visible"))return o.prop("aria-hidden",!0).slideUp(250),e.preventDefault(),!1}).on("click focus",function(){var e=i(this).parent(),o=e.find("span.description");e.addClass("currentTarget"),i(".woocommerce-input-wrapper:not(.currentTarget) span.description:visible").prop("aria-hidden",!0).slideUp(250),o.length&&o.is(":hidden")&&o.prop("aria-hidden",!1).slideDown(250),e.removeClass("currentTarget")}),i.scroll_to_notices=function(e){e.length&&i("html, body").animate({scrollTop:e.offset().top-100},1e3)}});