jQuery(function(p){if("undefined"==typeof wc_country_select_params)return!1;if(p().selectWoo){function t(){p("select.country_select:visible, select.state_select:visible").each(function(){var t=p.extend({placeholder:p(this).attr("data-placeholder")||p(this).attr("placeholder")||"",width:"100%"},{language:{errorLoading:function(){return wc_country_select_params.i18n_searching},inputTooLong:function(t){var e=t.input.length-t.maximum;return 1==e?wc_country_select_params.i18n_input_too_long_1:wc_country_select_params.i18n_input_too_long_n.replace("%qty%",e)},inputTooShort:function(t){var e=t.minimum-t.input.length;return 1==e?wc_country_select_params.i18n_input_too_short_1:wc_country_select_params.i18n_input_too_short_n.replace("%qty%",e)},loadingMore:function(){return wc_country_select_params.i18n_load_more},maximumSelected:function(t){return 1===t.maximum?wc_country_select_params.i18n_selection_too_long_1:wc_country_select_params.i18n_selection_too_long_n.replace("%qty%",t.maximum)},noResults:function(){return wc_country_select_params.i18n_no_matches},searching:function(){return wc_country_select_params.i18n_searching}}});p(this).on("select2:select",function(){p(this).focus()}).selectWoo(t)})}t(),p(document.body).bind("country_to_state_changed",function(){t()})}var e=wc_country_select_params.countries.replace(/&quot;/g,'"'),u=p.parseJSON(e),d=".woocommerce-billing-fields,.woocommerce-shipping-fields,.woocommerce-address-fields,.woocommerce-shipping-calculator";p(document.body).on("change refresh","select.country_to_state, input.country_to_state",function(){var t=p(this).closest(d);t.length||(t=p(this).closest(".form-row").parent());var e,n=p(this).val(),o=t.find("#billing_state, #shipping_state, #calc_shipping_state"),c=o.closest("p.form-row"),r=o.attr("name"),a=o.attr("id"),i=o.val(),s=o.attr("placeholder")||o.attr("data-placeholder")||"";if(u[n])if(p.isEmptyObject(u[n]))e=p('<input type="hidden" />').prop("id",a).prop("name",r).prop("placeholder",s).addClass("hidden"),c.hide().find(".select2-container").remove(),o.replaceWith(e),p(document.body).trigger("country_to_state_changed",[n,t]);else{var _=u[n],l=p('<option value=""></option>').text(wc_country_select_params.i18n_select_state_text);s=s||wc_country_select_params.i18n_select_state_text,c.show(),o.is("input")&&(e=p("<select></select>").prop("id",a).prop("name",r).data("placeholder",s).addClass("state_select"),o.replaceWith(e),o=t.find("#billing_state, #shipping_state, #calc_shipping_state")),o.empty().append(l),p.each(_,function(t){var e=p("<option></option>").prop("value",t).text(_[t]);o.append(e)}),o.val(i).change(),p(document.body).trigger("country_to_state_changed",[n,t])}else o.is('select, input[type="hidden"]')&&(e=p('<input type="text" />').prop("id",a).prop("name",r).prop("placeholder",s).addClass("input-text"),c.show().find(".select2-container").remove(),o.replaceWith(e),p(document.body).trigger("country_to_state_changed",[n,t]));p(document.body).trigger("country_to_state_changing",[n,t])}),p(document.body).on("wc_address_i18n_ready",function(){p(d).each(function(){var t=p(this).find("#billing_country, #shipping_country, #calc_shipping_country");0!==t.length&&0!==t.val().length&&t.trigger("refresh")})})});