/**
 * dashboard.scss
 * Styles for WooCommerce dashboard widgets, only loaded on the dashboard itself.
 */

/**
 * Imports
 */
@import "mixins";
@import "variables";
@import "fonts";

/**
 * Styling begins
 */
ul.woocommerce_stats {
	overflow: hidden;
	zoom: 1;

	li {
		width: 25%;
		padding: 0 1em;
		text-align: center;
		float: left;
		font-size: 0.8em;
		border-left: 1px solid #fff;
		border-right: 1px solid #ececec;
		box-sizing: border-box;
	}

	li:first-child {
		border-left: 0;
	}

	li:last-child {
		border-right: 0;
	}

	strong {
		font-family: Georgia, "Times New Roman", "Bitstream Charter", Times, serif;
		font-size: 4em;
		line-height: 1.2em;
		font-weight: normal;
		text-align: center;
		display: block;
	}
}

#woocommerce_dashboard_status {

	.inside {
		padding: 0;
		margin: 0;
	}

	.best-seller-this-month {

		a {

			strong {
				margin-right: 48px;
			}
		}
	}

	.wc_status_list {
		overflow: hidden;
		margin: 0;

		li {
			width: 50%;
			float: left;
			padding: 0;
			box-sizing: border-box;
			margin: 0;
			border-top: 1px solid #ececec;
			color: #aaa;

			a {
				display: block;
				color: #aaa;
				padding: 9px 12px;
				transition: all ease 0.5s;
				position: relative;
				font-size: 12px;

				.wc_sparkline {
					width: 4em;
					height: 2em;
					display: block;
					float: right;
					position: absolute;
					right: 0;
					top: 50%;
					margin-right: 12px;
					margin-top: -1.25em;
				}

				strong {
					font-size: 18px;
					line-height: 1.2em;
					font-weight: normal;
					display: block;
					color: #21759b;
				}

				&:hover {
					color: #2ea2cc;

					&::before,
					strong {
						color: #2ea2cc !important;
					}
				}

				&::before {

					@include icon();
					font-size: 2em;
					position: relative;
					width: auto;
					line-height: 1.2em;
					color: #464646;
					float: left;
					margin-right: 12px;
					margin-bottom: 12px;
				}
			}
		}

		li:first-child {
			border-top: 0;
		}

		li.sales-this-month {
			width: 100%;

			a::before {
				font-family: "Dashicons";
				content: "\f185";
			}
		}

		li.best-seller-this-month {
			width: 100%;

			a::before {
				content: "\e006";
			}
		}

		li.processing-orders {
			border-right: 1px solid #ececec;

			a::before {
				content: "\e011";
				color: $green;
			}
		}

		li.on-hold-orders {

			a::before {
				content: "\e033";
				color: #999;
			}
		}

		li.low-in-stock {
			border-right: 1px solid #ececec;

			a::before {
				content: "\e016";
				color: $orange;
			}
		}

		li.out-of-stock {

			a::before {
				content: "\e013";
				color: $red;
			}
		}
	}
}

#woocommerce_dashboard_recent_reviews {

	li {
		line-height: 1.5em;
		margin-bottom: 12px;
	}

	h4.meta {
		line-height: 1.4;
		margin: -0.2em 0 0 0;
		font-weight: normal;
		color: #999;
	}

	blockquote {
		padding: 0;
		margin: 0;
	}

	.avatar {
		float: left;
		margin: 0 10px 5px 0;
	}

	.star-rating {
		float: right;
		overflow: hidden;
		position: relative;
		height: 1.5em;
		line-height: 1.5;
		margin-left: 0.5em;
		width: 5.4em;
		font-family: "WooCommerce" !important;

		&::before {
			content: "\e021\e021\e021\e021\e021";
			color: darken(#ccc, 10%);
			float: left;
			top: 0;
			left: 0;
			position: absolute;
			letter-spacing: 0.1em;
			letter-spacing: 0\9; // IE8 & below hack ;-(
		}

		span {
			overflow: hidden;
			float: left;
			top: 0;
			left: 0;
			position: absolute;
			padding-top: 1.5em;
		}

		span::before {
			content: "\e020\e020\e020\e020\e020";
			top: 0;
			position: absolute;
			left: 0;
			letter-spacing: 0.1em;
			letter-spacing: 0\9; // IE8 & below hack ;-(
			color: #9c5d90;
		}
	}
}

#dash-right-now li.product-count a::before {
	font-family: "WooCommerce";
	content: "\e01d";
}
