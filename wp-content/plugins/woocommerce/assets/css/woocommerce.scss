/**
 * woocommerce.scss
 * Governs the general look and feel of WooCommerce sections of stores using themes that do not
 * integrate with WooCommerce specifically.
 */

/**
 * Imports
 */
@import "mixins";
@import "variables";
@import "animation";
@import "fonts";

/**
 * Global styles
 */
p.demo_store,
.woocommerce-store-notice {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	margin: 0;
	width: 100%;
	font-size: 1em;
	padding: 1em 0;
	text-align: center;
	background-color: $primary;
	color: $primarytext;
	z-index: 99998;
	box-shadow: 0 1px 1em rgba(0, 0, 0, 0.2);
	display: none;

	a {
		color: $primarytext;
		text-decoration: underline;
	}
}

.screen-reader-text {
	clip: rect(1px, 1px, 1px, 1px);
	height: 1px;
	overflow: hidden;
	position: absolute !important;
	width: 1px;
	word-wrap: normal !important;
}

.admin-bar p.demo_store {
	top: 32px;
}

/**
 * Utility classes
 */
.clear {
	clear: both;
}

/**
 * Main WooCommerce styles
 */
.woocommerce {

	.blockUI.blockOverlay {
		position: relative;

		@include loader();
	}

	.loader {

		@include loader();
	}

	a.remove {
		display: block;
		font-size: 1.5em;
		height: 1em;
		width: 1em;
		text-align: center;
		line-height: 1;
		border-radius: 100%;
		color: red !important; // Required for default theme compatibility
		text-decoration: none;
		font-weight: 700;
		border: 0;

		&:hover {
			color: #fff !important; // Required for default theme compatibility
			background: red;
		}
	}

	small.note {
		display: block;
		color: $subtext;
		font-size: 0.857em;
		margin-top: 10px;
	}

	.woocommerce-breadcrumb {

		@include clearfix();
		margin: 0 0 1em;
		padding: 0;
		font-size: 0.92em;
		color: $subtext;

		a {
			color: $subtext;
		}
	}

	.quantity .qty {
		width: 3.631em;
		text-align: center;
	}

	/**
	 * Product Page
	 */
	div.product {
		margin-bottom: 0;
		position: relative;

		.product_title {
			clear: none;
			margin-top: 0;
			padding: 0;
		}

		span.price,
		p.price {
			color: $highlight;
			font-size: 1.25em;

			ins {
				background: inherit;
				font-weight: 700;
				display: inline-block;
			}

			del {
				opacity: 0.5;
				display: inline-block;
			}
		}

		p.stock {
			font-size: 0.92em;
		}

		.stock {
			color: $highlight;
		}

		.out-of-stock {
			color: red;
		}

		.woocommerce-product-rating {
			margin-bottom: 1.618em;
		}

		div.images {
			margin-bottom: 2em;

			img {
				display: block;
				width: 100%;
				height: auto;
				box-shadow: none;
			}

			div.thumbnails {
				padding-top: 1em;
			}

			&.woocommerce-product-gallery {
				position: relative;
			}

			.woocommerce-product-gallery__wrapper {
				transition: all cubic-bezier(0.795, -0.035, 0, 1) 0.5s;
				margin: 0;
				padding: 0;
			}

			.woocommerce-product-gallery__wrapper .zoomImg {
				background-color: #fff;
				opacity: 0;
			}

			.woocommerce-product-gallery__image--placeholder {
				border: 1px solid #f2f2f2;
			}

			.woocommerce-product-gallery__image:nth-child(n+2) {
				width: 25%;
				display: inline-block;
			}

			.woocommerce-product-gallery__trigger {
				position: absolute;
				top: 0.5em;
				right: 0.5em;
				font-size: 2em;
				z-index: 9;
				width: 36px;
				height: 36px;
				background: #fff;
				text-indent: -9999px;
				border-radius: 100%;
				box-sizing: content-box;

				&::before {
					content: "";
					display: block;
					width: 10px;
					height: 10px;
					border: 2px solid #000;
					border-radius: 100%;
					position: absolute;
					top: 9px;
					left: 9px;
					box-sizing: content-box;
				}

				&::after {
					content: "";
					display: block;
					width: 2px;
					height: 8px;
					background: #000;
					border-radius: 6px;
					position: absolute;
					top: 19px;
					left: 22px;
					transform: rotate(-45deg);
					box-sizing: content-box;
				}
			}

			.flex-control-thumbs {
				overflow: hidden;
				zoom: 1;
				margin: 0;
				padding: 0;

				li {
					width: 25%;
					float: left;
					margin: 0;
					list-style: none;

					img {
						cursor: pointer;
						opacity: 0.5;
						margin: 0;

						&.flex-active,
						&:hover {
							opacity: 1;
						}
					}
				}
			}
		}

		.woocommerce-product-gallery--columns-3 {

			.flex-control-thumbs li:nth-child(3n+1) {
				clear: left;
			}
		}

		.woocommerce-product-gallery--columns-4 {

			.flex-control-thumbs li:nth-child(4n+1) {
				clear: left;
			}
		}

		.woocommerce-product-gallery--columns-5 {

			.flex-control-thumbs li:nth-child(5n+1) {
				clear: left;
			}
		}

		div.summary {
			margin-bottom: 2em;
		}

		div.social {
			text-align: right;
			margin: 0 0 1em;

			span {
				margin: 0 0 0 2px;

				span {
					margin: 0;
				}

				.stButton .chicklets {
					padding-left: 16px;
					width: 0;
				}
			}

			iframe {
				float: left;
				margin-top: 3px;
			}
		}

		.woocommerce-tabs {

			ul.tabs {
				list-style: none;
				padding: 0 0 0 1em;
				margin: 0 0 1.618em;
				overflow: hidden;
				position: relative;

				li {
					border: 1px solid darken($secondary, 10%);
					background-color: $secondary;
					display: inline-block;
					position: relative;
					z-index: 0;
					border-radius: 4px 4px 0 0;
					margin: 0 -5px;
					padding: 0 1em;

					a {
						display: inline-block;
						padding: 0.5em 0;
						font-weight: 700;
						color: $secondarytext;
						text-decoration: none;

						&:hover {
							text-decoration: none;
							color: lighten($secondarytext, 10%);
						}
					}

					&.active {
						background: $contentbg;
						z-index: 2;
						border-bottom-color: $contentbg;

						a {
							color: inherit;
							text-shadow: inherit;
						}

						&::before {
							box-shadow: 2px 2px 0 $contentbg;
						}

						&::after {
							box-shadow: -2px 2px 0 $contentbg;
						}
					}

					&::before,
					&::after {
						border: 1px solid darken($secondary, 10%);
						position: absolute;
						bottom: -1px;
						width: 5px;
						height: 5px;
						content: " ";
						box-sizing: border-box;
					}

					&::before {
						left: -5px;
						border-bottom-right-radius: 4px;
						border-width: 0 1px 1px 0;
						box-shadow: 2px 2px 0 $secondary;
					}

					&::after {
						right: -5px;
						border-bottom-left-radius: 4px;
						border-width: 0 0 1px 1px;
						box-shadow: -2px 2px 0 $secondary;
					}
				}

				&::before {
					position: absolute;
					content: " ";
					width: 100%;
					bottom: 0;
					left: 0;
					border-bottom: 1px solid darken($secondary, 10%);
					z-index: 1;
				}
			}

			.panel {
				margin: 0 0 2em;
				padding: 0;
			}
		}

		p.cart {
			margin-bottom: 2em;

			@include clearfix();
		}

		form.cart {
			margin-bottom: 2em;

			@include clearfix();

			div.quantity {
				float: left;
				margin: 0 4px 0 0;
			}

			table {
				border-width: 0 0 1px;

				td {
					padding-left: 0;
				}

				div.quantity {
					float: none;
					margin: 0;
				}

				small.stock {
					display: block;
					float: none;
				}
			}

			.variations {
				margin-bottom: 1em;
				border: 0;
				width: 100%;

				td,
				th {
					border: 0;
					vertical-align: top;
					line-height: 2em;
				}

				label {
					font-weight: 700;
				}

				select {
					max-width: 100%;
					min-width: 75%;
					display: inline-block;
					margin-right: 1em;
				}

				td.label {
					padding-right: 1em;
				}
			}

			.woocommerce-variation-description p {
				margin-bottom: 1em;
			}

			.reset_variations {
				visibility: hidden;
				font-size: 0.83em;
			}

			.wc-no-matching-variations {
				display: none;
			}

			.button {
				vertical-align: middle;
				float: left;
			}

			.group_table {

				td.woocommerce-grouped-product-list-item__label {
					padding-right: 1em;
					padding-left: 1em;
				}

				td {
					vertical-align: top;
					padding-bottom: 0.5em;
					border: 0;
				}

				td:first-child {
					width: 4em;
					text-align: center;
				}

				.wc-grouped-product-add-to-cart-checkbox {
					display: inline-block;
					width: auto;
					margin: 0 auto;
					transform: scale(1.5, 1.5);
				}
			}
		}
	}

	span.onsale {
		min-height: 3.236em;
		min-width: 3.236em;
		padding: 0.202em;
		font-size: 1em;
		font-weight: 700;
		position: absolute;
		text-align: center;
		line-height: 3.236;
		top: -0.5em;
		left: -0.5em;
		margin: 0;
		border-radius: 100%;
		background-color: $highlight;
		color: $highlightext;
		font-size: 0.857em;
		z-index: 9;
	}

	/**
	 * Product loops
	 */
	.products ul,
	ul.products {
		margin: 0 0 1em;
		padding: 0;
		list-style: none outside;
		clear: both;

		@include clearfix();

		li {
			list-style: none outside;
		}
	}

	ul.products li.product {

		.onsale {
			top: 0;
			right: 0;
			left: auto;
			margin: -0.5em -0.5em 0 0;
		}

		h3,
		.woocommerce-loop-product__title,
		.woocommerce-loop-category__title {
			padding: 0.5em 0;
			margin: 0;
			font-size: 1em;
		}

		a {
			text-decoration: none;
		}

		a img {
			width: 100%;
			height: auto;
			display: block;
			margin: 0 0 1em;
			box-shadow: none;
		}

		strong {
			display: block;
		}

		.woocommerce-placeholder {
			border: 1px solid #f2f2f2;
		}

		.star-rating {
			font-size: 0.857em;
		}

		.button {
			margin-top: 1em;
		}

		.price {
			color: $highlight;
			display: block;
			font-weight: normal;
			margin-bottom: 0.5em;
			font-size: 0.857em;

			del {
				color: inherit;
				opacity: 0.5;
				display: inline-block;
			}

			ins {
				background: none;
				font-weight: 700;
				display: inline-block;
			}

			.from {
				font-size: 0.67em;
				margin: -2px 0 0 0;
				text-transform: uppercase;
				color: rgba(desaturate($highlight, 75%), 0.5);
			}
		}
	}

	.woocommerce-result-count {
		margin: 0 0 1em;
	}

	.woocommerce-ordering {
		margin: 0 0 1em;

		select {
			vertical-align: top;
		}
	}

	nav.woocommerce-pagination {
		text-align: center;

		ul {
			display: inline-block;
			white-space: nowrap;
			padding: 0;
			clear: both;
			border: 1px solid darken($secondary, 10%);
			border-right: 0;
			margin: 1px;

			li {
				border-right: 1px solid darken($secondary, 10%);
				padding: 0;
				margin: 0;
				float: left;
				display: inline;
				overflow: hidden;

				a,
				span {
					margin: 0;
					text-decoration: none;
					padding: 0;
					line-height: 1;
					font-size: 1em;
					font-weight: normal;
					padding: 0.5em;
					min-width: 1em;
					display: block;
				}

				span.current,
				a:hover,
				a:focus {
					background: $secondary;
					color: darken($secondary, 40%);
				}
			}
		}
	}

	/**
	 * Buttons
	 */
	a.button,
	button.button,
	input.button,
	#respond input#submit {
		font-size: 100%;
		margin: 0;
		line-height: 1;
		cursor: pointer;
		position: relative;
		text-decoration: none;
		overflow: visible;
		padding: 0.618em 1em;
		font-weight: 700;
		border-radius: 3px;
		left: auto;
		color: $secondarytext;
		background-color: $secondary;
		border: 0;
		display: inline-block;
		background-image: none;
		box-shadow: none;
		text-shadow: none;

		&.loading {
			opacity: 0.25;
			padding-right: 2.618em;

			&::after {
				font-family: "WooCommerce";
				content: "\e01c";
				vertical-align: top;
				font-weight: 400;
				position: absolute;
				top: 0.618em;
				right: 1em;
				animation: spin 2s linear infinite;
			}
		}

		&.added::after {
			font-family: "WooCommerce";
			content: "\e017";
			margin-left: 0.53em;
			vertical-align: bottom;
		}

		&:hover {
			background-color: darken( $secondary, 5% );
			text-decoration: none;
			background-image: none;
			color: $secondarytext;
		}

		&.alt {
			background-color: $primary;
			color: $primarytext;
			-webkit-font-smoothing: antialiased;

			&:hover {
				background-color: darken( $primary, 5% );
				color: $primarytext;
			}

			&.disabled,
			&:disabled,
			&:disabled[disabled],
			&.disabled:hover,
			&:disabled:hover,
			&:disabled[disabled]:hover {
				background-color: $primary;
				color: $primarytext;
			}
		}

		&:disabled,
		&.disabled,
		&:disabled[disabled] {
			color: inherit;
			cursor: not-allowed;
			opacity: 0.5;
			padding: 0.618em 1em;

			&:hover {
				color: inherit;
				background-color: $secondary;
			}
		}
	}

	.cart .button,
	.cart input.button {
		float: none;
	}

	a.added_to_cart {
		padding-top: 0.5em;
		display: inline-block;
	}

	/**
	 * Reviews
	 */
	#reviews {

		h2 small {
			float: right;
			color: $subtext;
			font-size: 15px;
			margin: 10px 0 0;

			a {
				text-decoration: none;
				color: $subtext;
			}
		}

		h3 {
			margin: 0;
		}

		#respond {
			margin: 0;
			border: 0;
			padding: 0;
		}

		#comment {
			height: 75px;
		}

		#comments {

			.add_review {

				@include clearfix();
			}

			h2 {
				clear: none;
			}

			ol.commentlist {

				@include clearfix();
				margin: 0;
				width: 100%;
				background: none;
				list-style: none;

				li {
					padding: 0;
					margin: 0 0 20px;
					border: 0;
					position: relative;
					background: 0;
					border: 0;

					.meta {
						color: $subtext;
						font-size: 0.75em;
					}

					img.avatar {
						float: left;
						position: absolute;
						top: 0;
						left: 0;
						padding: 3px;
						width: 32px;
						height: auto;
						background: $secondary;
						border: 1px solid darken($secondary, 3%);
						margin: 0;
						box-shadow: none;
					}

					.comment-text {
						margin: 0 0 0 50px;
						border: 1px solid darken($secondary, 3%);
						border-radius: 4px;
						padding: 1em 1em 0;

						@include clearfix();

						p {
							margin: 0 0 1em;
						}

						p.meta {
							font-size: 0.83em;
						}
					}
				}

				ul.children {
					list-style: none outside;
					margin: 20px 0 0 50px;

					.star-rating {
						display: none;
					}
				}

				#respond {
					border: 1px solid darken($secondary, 3%);
					border-radius: 4px;
					padding: 1em 1em 0;
					margin: 20px 0 0 50px;
				}
			}

			.commentlist > li::before {
				content: "";
			}
		}
	}

	/**
	 * Star ratings
	 */
	.star-rating {
		float: right;
		overflow: hidden;
		position: relative;
		height: 1em;
		line-height: 1;
		font-size: 1em;
		width: 5.4em;
		font-family: "star";

		&::before {
			content: "\73\73\73\73\73";
			color: darken($secondary, 10%);
			float: left;
			top: 0;
			left: 0;
			position: absolute;
		}

		span {
			overflow: hidden;
			float: left;
			top: 0;
			left: 0;
			position: absolute;
			padding-top: 1.5em;
		}

		span::before {
			content: "\53\53\53\53\53";
			top: 0;
			position: absolute;
			left: 0;
		}
	}

	.woocommerce-product-rating {

		@include clearfix();
		line-height: 2;
		display: block;

		.star-rating {
			margin: 0.5em 4px 0 0;
			float: left;
		}
	}

	.products .star-rating {
		display: block;
		margin: 0 0 0.5em;
		float: none;
	}

	.hreview-aggregate .star-rating {
		margin: 10px 0 0;
	}

	#review_form #respond {

		@include clearfix();
		position: static;
		margin: 0;
		width: auto;
		padding: 0;
		background: transparent none;
		border: 0;

		p {
			margin: 0 0 10px;
		}

		.form-submit input {
			left: auto;
		}

		textarea {
			box-sizing: border-box;
			width: 100%;
		}
	}

	p.stars {

		a {
			position: relative;
			height: 1em;
			width: 1em;
			text-indent: -999em;
			display: inline-block;
			text-decoration: none;

			&::before {
				display: block;
				position: absolute;
				top: 0;
				left: 0;
				width: 1em;
				height: 1em;
				line-height: 1;
				font-family: "WooCommerce";
				content: "\e021";
				text-indent: 0;
			}

			&:hover ~ a::before {
				content: "\e021";
			}
		}

		&:hover a::before {
			content: "\e020";
		}

		&.selected {

			a.active {

				&::before {
					content: "\e020";
				}

				~ a::before {
					content: "\e021";
				}
			}

			a:not(.active)::before {
				content: "\e020";
			}
		}
	}

	/**
	 * Tables
	 */
	table.shop_attributes {
		border: 0;
		border-top: 1px dotted rgba(0, 0, 0, 0.1);
		margin-bottom: 1.618em;
		width: 100%;

		th {
			width: 150px;
			font-weight: 700;
			padding: 8px;
			border-top: 0;
			border-bottom: 1px dotted rgba(0, 0, 0, 0.1);
			margin: 0;
			line-height: 1.5;
		}

		td {
			font-style: italic;
			padding: 0;
			border-top: 0;
			border-bottom: 1px dotted rgba(0, 0, 0, 0.1);
			margin: 0;
			line-height: 1.5;

			p {
				margin: 0;
				padding: 8px 0;
			}
		}

		tr:nth-child(even) td,
		tr:nth-child(even) th {
			background: rgba(0, 0, 0, 0.025);
		}
	}

	table.shop_table {
		border: 1px solid rgba(0, 0, 0, 0.1);
		margin: 0 -1px 24px 0;
		text-align: left;
		width: 100%;
		border-collapse: separate;
		border-radius: 5px;

		th {
			font-weight: 700;
			padding: 9px 12px;
			line-height: 1.5em;
		}

		td {
			border-top: 1px solid rgba(0, 0, 0, 0.1);
			padding: 9px 12px;
			vertical-align: middle;
			line-height: 1.5em;

			small {
				font-weight: normal;
			}
		}

		tbody:first-child tr:first-child {

			th,
			td {
				border-top: 0;
			}
		}

		tfoot td,
		tfoot th,
		tbody th {
			font-weight: 700;
			border-top: 1px solid rgba(0, 0, 0, 0.1);
		}
	}

	table.my_account_orders {
		font-size: 0.85em;

		th,
		td {
			padding: 4px 8px;
			vertical-align: middle;
		}

		.button {
			white-space: nowrap;
		}

		.order-actions {
			text-align: right;

			.button {
				margin: 0.125em 0 0.125em 0.25em;
			}
		}
	}

	table.woocommerce-MyAccount-downloads {

		td,
		th {
			vertical-align: top;
			text-align: center;

			&:first-child {
				text-align: left;
			}

			&:last-child {
				text-align: left;
			}

			.woocommerce-MyAccount-downloads-file::before {
				content: "\2193";
				display: inline-block;
			}
		}
	}

	td.product-name {

		dl.variation,
		.wc-item-meta {
			list-style: none outside;

			dt,
			.wc-item-meta-label {
				float: left;
				clear: both;
				margin-right: 0.25em;
				display: inline-block;
				list-style: none outside;
			}

			dd {
				margin: 0;
			}

			p,
			&:last-child {
				margin-bottom: 0;
			}
		}

		p.backorder_notification {
			font-size: 0.83em;
		}
	}

	td.product-quantity {
		min-width: 80px;
	}

	/**
	 * Cart sidebar
	 */
	ul.cart_list,
	ul.product_list_widget {
		list-style: none outside;
		padding: 0;
		margin: 0;

		li {
			padding: 4px 0;
			margin: 0;

			@include clearfix();
			list-style: none;

			a {
				display: block;
				font-weight: 700;
			}

			img {
				float: right;
				margin-left: 4px;
				width: 32px;
				height: auto;
				box-shadow: none;
			}

			dl {
				margin: 0;
				padding-left: 1em;
				border-left: 2px solid rgba(0, 0, 0, 0.1);

				@include clearfix();

				dt,
				dd {
					display: inline-block;
					float: left;
					margin-bottom: 1em;
				}

				dt {
					font-weight: 700;
					padding: 0 0 0.25em;
					margin: 0 4px 0 0;
					clear: left;
				}

				dd {
					padding: 0 0 0.25em;

					p:last-child {
						margin-bottom: 0;
					}
				}
			}

			.star-rating {
				float: none;
			}
		}
	}

	&.widget_shopping_cart,
	.widget_shopping_cart {

		.total {
			border-top: 3px double $secondary;
			padding: 4px 0 0;

			strong {
				min-width: 40px;
				display: inline-block;
			}
		}

		.cart_list li {
			padding-left: 2em;
			position: relative;
			padding-top: 0;

			a.remove {
				position: absolute;
				top: 0;
				left: 0;
			}
		}

		.buttons {

			@include clearfix();

			a {
				margin-right: 5px;
				margin-bottom: 5px;
			}
		}
	}

	/**
	 * Forms
	 */
	form .form-row {
		padding: 3px;
		margin: 0 0 6px;

		[placeholder]:focus::-webkit-input-placeholder {
			transition: opacity 0.5s 0.5s ease;
			opacity: 0;
		}

		label {
			line-height: 2;
		}

		label.hidden {
			visibility: hidden;
		}

		label.inline {
			display: inline;
		}

		.woocommerce-input-wrapper {

			.description {
				background: #1e85be;
				color: #fff;
				border-radius: 3px;
				padding: 1em;
				margin: 0.5em 0 0;
				clear: both;
				display: none;
				position: relative;

				a {
					color: #fff;
					text-decoration: underline;
					border: 0;
					box-shadow: none;
				}

				&::before {
					left: 50%;
					top: 0%;
					margin-top: -4px;
					transform: translateX(-50%) rotate(180deg);
					content: "";
					position: absolute;
					border-width: 4px 6px 0 6px;
					border-style: solid;
					border-color: #1e85be transparent transparent transparent;
					z-index: 100;
					display: block;
				}
			}
		}

		select {
			cursor: pointer;
			margin: 0;
		}

		.required {
			color: red;
			font-weight: 700;
			border: 0 !important;
			text-decoration: none;
			visibility: hidden; // Only show optional by default.
		}

		.optional {
			visibility: visible;
		}

		.input-checkbox {
			display: inline;
			margin: -2px 8px 0 0;
			text-align: center;
			vertical-align: middle;
		}

		input.input-text,
		textarea {
			box-sizing: border-box;
			width: 100%;
			margin: 0;
			outline: 0;
			line-height: normal;
		}

		textarea {
			height: 4em;
			line-height: 1.5;
			display: block;
			box-shadow: none;
		}

		.select2-container {
			width: 100%;
			line-height: 2em;
		}

		&.woocommerce-invalid {

			label {
				color: $red;
			}

			.select2-container,
			input.input-text,
			select {
				border-color: $red;
			}
		}

		&.woocommerce-validated {

			.select2-container,
			input.input-text,
			select {
				border-color: darken($green, 5%);
			}
		}

		::-webkit-input-placeholder {
			line-height: normal;
		}

		:-moz-placeholder {
			line-height: normal;
		}

		:-ms-input-placeholder {
			line-height: normal;
		}
	}

	form.login,
	form.checkout_coupon,
	form.register {
		border: 1px solid darken($secondary, 10%);
		padding: 20px;
		margin: 2em 0;
		text-align: left;
		border-radius: 5px;
	}

	ul#shipping_method {
		list-style: none outside;
		margin: 0;
		padding: 0;

		li {
			margin: 0 0 0.5em;
			line-height: 1.5em;
			list-style: none outside;

			input {
				margin: 3px 0.4375em 0 0;
				vertical-align: top;
			}

			label {
				display: inline;
			}
		}

		.amount {
			font-weight: 700;
		}
	}

	p.woocommerce-shipping-contents {
		margin: 0;
	}

	/**
	 * Order page
	 */
	ul.order_details {

		@include clearfix();
		margin: 0 0 3em;
		list-style: none;

		li {
			float: left;
			margin-right: 2em;
			text-transform: uppercase;
			font-size: 0.715em;
			line-height: 1;
			border-right: 1px dashed darken($secondary, 10%);
			padding-right: 2em;
			margin-left: 0;
			padding-left: 0;
			list-style-type: none;

			strong {
				display: block;
				font-size: 1.4em;
				text-transform: none;
				line-height: 1.5;
			}

			&:last-of-type {
				border: none;
			}
		}
	}

	.wc-bacs-bank-details-account-name {
		font-weight: bold;
	}

	.woocommerce-order-downloads,
	.woocommerce-customer-details,
	.woocommerce-order-details {
		margin-bottom: 2em;

		*:last-child {
			margin-bottom: 0;
		}
	}

	.woocommerce-customer-details {

		address {
			font-style: normal;
			margin-bottom: 0;
			border: 1px solid rgba(0, 0, 0, 0.1);
			border-bottom-width: 2px;
			border-right-width: 2px;
			text-align: left;
			width: 100%;
			border-radius: 5px;
			padding: 6px 12px;
		}

		.woocommerce-customer-details--phone,
		.woocommerce-customer-details--email {
			margin-bottom: 0;
			padding-left: 1.5em;
		}

		.woocommerce-customer-details--phone::before {

			@include iconbefore( "\e037" );
			margin-left: -1.5em;
			line-height: 1.75;
			position: absolute;
		}

		.woocommerce-customer-details--email::before {

			@include iconbefore( "\e02d" );
			margin-left: -1.5em;
			line-height: 1.75;
			position: absolute;
		}
	}

	/**
	 * Layered nav widget
	 */
	.woocommerce-widget-layered-nav-list {
		margin: 0;
		padding: 0;
		border: 0;
		list-style: none outside;

		.woocommerce-widget-layered-nav-list__item {

			@include clearfix();
			padding: 0 0 1px;
			list-style: none;

			a,
			span {
				padding: 1px 0;
			}
		}

		.woocommerce-widget-layered-nav-list__item--chosen a::before {

			@include iconbefore( "\e013" );
			color: $red;
		}
	}

	.woocommerce-widget-layered-nav-dropdown__submit {
		margin-top: 1em;
	}

	.widget_layered_nav_filters ul {
		margin: 0;
		padding: 0;
		border: 0;
		list-style: none outside;
		overflow: hidden;
		zoom: 1;

		li {
			float: left;
			padding: 0 1em 1px 1px;
			list-style: none;

			a {
				text-decoration: none;

				&::before {

					@include iconbefore( "\e013" );
					color: $red;
					vertical-align: inherit;
					margin-right: 0.5em;
				}
			}
		}
	}

	/**
	 * Price filter widget
	 */
	.widget_price_filter {

		.price_slider {
			margin-bottom: 1em;
		}

		.price_slider_amount {
			text-align: right;
			line-height: 2.4;
			font-size: 0.8751em;

			.button {
				font-size: 1.15em;
				float: left;
			}
		}

		.ui-slider {
			position: relative;
			text-align: left;
			margin-left: 0.5em;
			margin-right: 0.5em;
		}

		.ui-slider .ui-slider-handle {
			position: absolute;
			z-index: 2;
			width: 1em;
			height: 1em;
			background-color: $primary;
			border-radius: 1em;
			cursor: ew-resize;
			outline: none;
			top: -0.3em;

			/* rtl:ignore */
			margin-left: -0.5em;
		}

		.ui-slider .ui-slider-range {
			position: absolute;
			z-index: 1;
			font-size: 0.7em;
			display: block;
			border: 0;
			border-radius: 1em;
			background-color: $primary;
		}

		.price_slider_wrapper .ui-widget-content {
			border-radius: 1em;
			background-color: darken( $primary, 30% );
			border: 0;
		}

		.ui-slider-horizontal {
			height: 0.5em;
		}

		.ui-slider-horizontal .ui-slider-range {
			top: 0;
			height: 100%;
		}

		.ui-slider-horizontal .ui-slider-range-min {
			left: -1px;
		}

		.ui-slider-horizontal .ui-slider-range-max {
			right: -1px;
		}
	}

	/**
	 * Rating Filter Widget
	 */
	.widget_rating_filter ul {
		margin: 0;
		padding: 0;
		border: 0;
		list-style: none outside;

		li {

			@include clearfix();
			padding: 0 0 1px;
			list-style: none;

			a {
				padding: 1px 0;
				text-decoration: none;
			}

			.star-rating {
				float: none;
				display: inline-block;
			}
		}

		li.chosen a::before {

			@include iconbefore( "\e013" );
			color: $red;
		}
	}

	.woocommerce-form-login {

		.woocommerce-form-login__submit {
			float: left;
			margin-right: 1em;
		}

		.woocommerce-form-login__rememberme {
			display: inline-block;
		}
	}
}

.woocommerce-no-js {

	form.woocommerce-form-login,
	form.woocommerce-form-coupon {
		display: block !important;
	}

	.woocommerce-form-login-toggle,
	.woocommerce-form-coupon-toggle,
	.showcoupon {
		display: none !important;
	}
}

.woocommerce-message,
.woocommerce-error,
.woocommerce-info {
	padding: 1em 2em 1em 3.5em;
	margin: 0 0 2em;
	position: relative;
	background-color: lighten($secondary, 5%);
	color: $secondarytext;
	border-top: 3px solid $primary;
	list-style: none outside;

	@include clearfix();
	width: auto;
	word-wrap: break-word;

	&::before {
		font-family: "WooCommerce";
		content: "\e028";
		display: inline-block;
		position: absolute;
		top: 1em;
		left: 1.5em;
	}

	.button {
		float: right;
	}

	li {
		list-style: none outside !important; // Required for default theme compatibility
		padding-left: 0 !important; // Required for default theme compatibility
		margin-left: 0 !important; // Required for default theme compatibility
	}
}

/**
 * Right to left styles
 */
.rtl.woocommerce .price_label,
.rtl.woocommerce .price_label span {

	/* rtl:ignore */
	direction: ltr;
	unicode-bidi: embed;
}

.woocommerce-message {
	border-top-color: #8fae1b;

	&::before {
		content: "\e015";
		color: #8fae1b;
	}
}

.woocommerce-info {
	border-top-color: #1e85be;

	&::before {
		color: #1e85be;
	}
}

.woocommerce-error {
	border-top-color: #b81c23;

	&::before {
		content: "\e016";
		color: #b81c23;
	}
}

/**
 * Account page
 */
.woocommerce-account {

	.woocommerce {

		@include clearfix();
	}

	.addresses .title {

		@include clearfix();

		h3 {
			float: left;
		}

		.edit {
			float: right;
		}
	}

	ol.commentlist.notes li.note {

		p.meta {
			font-weight: 700;
			margin-bottom: 0;
		}

		.description p:last-child {
			margin-bottom: 0;
		}
	}

	ul.digital-downloads {
		margin-left: 0;
		padding-left: 0;

		li {
			list-style: none;
			margin-left: 0;
			padding-left: 0;

			&::before {

				@include iconbefore( "\e00a" );
			}

			.count {
				float: right;
			}
		}
	}
}

/**
 * Cart/checkout page
 */
.woocommerce-cart,
.woocommerce-checkout,
#add_payment_method {

	table.cart {

		.product-thumbnail {
			min-width: 32px;
		}

		img {
			width: 32px;
			box-shadow: none;
		}

		th,
		td {
			vertical-align: middle;
		}

		td.actions .coupon .input-text {
			float: left;
			box-sizing: border-box;
			border: 1px solid darken($secondary, 10%);
			padding: 6px 6px 5px;
			margin: 0 4px 0 0;
			outline: 0;
		}

		input {
			margin: 0;
			vertical-align: middle;
		}
	}

	.wc-proceed-to-checkout {

		@include clearfix;
		padding: 1em 0;

		a.checkout-button {
			display: block;
			text-align: center;
			margin-bottom: 1em;
			font-size: 1.25em;
			padding: 1em;
		}
	}

	.cart-collaterals {

		.shipping-calculator-button {
			float: none;
			margin-top: 0.5em;
			display: inline-block;
		}

		.shipping-calculator-button::after {

			@include iconafter( "\e019" );
		}

		.shipping-calculator-form {
			margin: 1em 0 0 0;
		}

		.cart_totals {

			p small {
				color: $subtext;
				font-size: 0.83em;
			}

			table {
				border-collapse: separate;
				margin: 0 0 6px;
				padding: 0;

				tr:first-child {

					th,
					td {
						border-top: 0;
					}
				}

				th {
					width: 35%;
				}

				td,
				th {
					vertical-align: top;
					border-left: 0;
					border-right: 0;
					line-height: 1.5em;
				}

				small {
					color: $subtext;
				}

				select {
					width: 100%;
				}
			}

			.discount td {
				color: $highlight;
			}

			tr td,
			tr th {
				border-top: 1px solid $secondary;
			}

			.woocommerce-shipping-destination {
				margin-bottom: 0;
			}
		}

		.cross-sells ul.products li.product {
			margin-top: 0;
		}
	}

	.checkout {

		.col-2 {

			h3#ship-to-different-address {
				float: left;
				clear: none;
			}

			.notes {
				clear: left;
			}

			.form-row-first {
				clear: left;
			}
		}

		.create-account small {
			font-size: 11px;
			color: $subtext;
			font-weight: normal;
		}

		div.shipping-address {
			padding: 0;
			clear: left;
			width: 100%;
		}

		.shipping_address {
			clear: both;
		}
	}

	#payment {
		background: $secondary;
		border-radius: 5px;

		ul.payment_methods {

			@include clearfix();
			text-align: left;
			padding: 1em;
			border-bottom: 1px solid darken($secondary, 10%);
			margin: 0;
			list-style: none outside;

			li {
				line-height: 2;
				text-align: left;
				margin: 0;
				font-weight: normal;

				input {
					margin: 0 1em 0 0;
				}

				img {
					vertical-align: middle;
					margin: -2px 0 0 0.5em;
					padding: 0;
					position: relative;
					box-shadow: none;
				}

				img + img {
					margin-left: 2px;
				}
			}

			li:not(.woocommerce-notice) {

				@include clearfix;
			}
		}

		div.form-row {
			padding: 1em;
		}

		div.payment_box {
			position: relative;
			box-sizing: border-box;
			width: 100%;
			padding: 1em;
			margin: 1em 0;
			font-size: 0.92em;
			border-radius: 2px;
			line-height: 1.5;
			background-color: darken($secondary, 5%);
			color: $secondarytext;

			input.input-text,
			textarea {
				border-color: darken($secondary, 15%);
				border-top-color: darken($secondary, 20%);
			}

			::-webkit-input-placeholder {
				color: darken($secondary, 20%);
			}

			:-moz-placeholder {
				color: darken($secondary, 20%);
			}

			:-ms-input-placeholder {
				color: darken($secondary, 20%);
			}

			.woocommerce-SavedPaymentMethods {
				list-style: none outside;
				margin: 0;

				.woocommerce-SavedPaymentMethods-token,
				.woocommerce-SavedPaymentMethods-new {
					margin: 0 0 0.5em;

					label {
						cursor: pointer;
					}
				}

				.woocommerce-SavedPaymentMethods-tokenInput {
					vertical-align: middle;
					margin: -3px 1em 0 0;
					position: relative;
				}
			}

			.wc-credit-card-form {
				border: 0;
				padding: 0;
				margin: 1em 0 0;
			}

			.wc-credit-card-form-card-number,
			.wc-credit-card-form-card-expiry,
			.wc-credit-card-form-card-cvc {
				font-size: 1.5em;
				padding: 8px;
				background-repeat: no-repeat;
				background-position: right 0.618em center;
				background-size: 32px 20px;

				&.visa {
					background-image: url("../images/icons/credit-cards/visa.svg");
				}

				&.mastercard {
					background-image: url("../images/icons/credit-cards/mastercard.svg");
				}

				&.laser {
					background-image: url("../images/icons/credit-cards/laser.svg");
				}

				&.dinersclub {
					background-image: url("../images/icons/credit-cards/diners.svg");
				}

				&.maestro {
					background-image: url("../images/icons/credit-cards/maestro.svg");
				}

				&.jcb {
					background-image: url("../images/icons/credit-cards/jcb.svg");
				}

				&.amex {
					background-image: url("../images/icons/credit-cards/amex.svg");
				}

				&.discover {
					background-image: url("../images/icons/credit-cards/discover.svg");
				}
			}

			span.help {
				font-size: 0.857em;
				color: $subtext;
				font-weight: normal;
			}

			.form-row {
				margin: 0 0 1em;
			}

			p:last-child {
				margin-bottom: 0;
			}

			&::before {
				content: "";
				display: block;
				border: 1em solid darken($secondary, 5%); /* arrow size / color */
				border-right-color: transparent;
				border-left-color: transparent;
				border-top-color: transparent;
				position: absolute;
				top: -0.75em;
				left: 0;
				margin: -1em 0 0 2em;
			}
		}

		.payment_method_paypal {

			.about_paypal {
				float: right;
				line-height: 52px;
				font-size: 0.83em;
			}

			img {
				max-height: 52px;
				vertical-align: middle;
			}
		}
	}
}

.woocommerce-terms-and-conditions {
	border: 1px solid rgba(0, 0, 0, 0.2);
	box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
	background: rgba(0, 0, 0, 0.05);
}

.woocommerce-invalid {

	#terms {
		outline: 2px solid red;
		outline-offset: 2px;
	}
}

/**
 * Password strength meter
 */
.woocommerce-password-strength {
	text-align: center;
	font-weight: 600;
	padding: 3px 0.5em;
	font-size: 1em;

	&.strong {
		background-color: #c1e1b9;
		border-color: #83c373;
	}

	&.short {
		background-color: #f1adad;
		border-color: #e35b5b;
	}

	&.bad {
		background-color: #fbc5a9;
		border-color: #f78b53;
	}

	&.good {
		background-color: #ffe399;
		border-color: #ffc733;
	}
}

.woocommerce-password-hint {
	margin: 0.5em 0 0;
	display: block;
}

/**
 * Twenty Eleven specific styles
 */
#content.twentyeleven .woocommerce-pagination a {
	font-size: 1em;
	line-height: 1;
}

/**
 * Twenty Thirteen specific styles
 */
.single-product .twentythirteen {

	.entry-summary,
	#reply-title,
	#respond #commentform {
		padding: 0;
	}

	p.stars {
		clear: both;
	}
}

.twentythirteen .woocommerce-breadcrumb {
	padding-top: 40px;
}

/**
 * Twenty Fourteen specific styles
 */
.twentyfourteen ul.products li.product {
	margin-top: 0 !important;
}

/**
 * Twenty Sixteen specific styles
 */
body:not(.search-results) .twentysixteen .entry-summary {
	color: inherit;
	font-size: inherit;
	line-height: inherit;
}

.twentysixteen .price ins {
	background: inherit;
	color: inherit;
}
