<!doctype html>

<title>CodeMirror: HTML mixed mode</title>
<meta charset="utf-8"/>
<link rel=stylesheet href="../../doc/docs.css">

<link rel="stylesheet" href="../../lib/codemirror.css">
<script src="../../lib/codemirror.js"></script>
<script src="../../addon/selection/selection-pointer.js"></script>
<script src="../xml/xml.js"></script>
<script src="../javascript/javascript.js"></script>
<script src="../css/css.js"></script>
<script src="../vbscript/vbscript.js"></script>
<script src="htmlmixed.js"></script>
<style>.CodeMirror {border-top: 1px solid black; border-bottom: 1px solid black;}</style>
<div id=nav>
  <a href="https://codemirror.net"><h1>CodeMirror</h1><img id=logo src="../../doc/logo.png"></a>

  <ul>
    <li><a href="../../index.html">Home</a>
    <li><a href="../../doc/manual.html">Manual</a>
    <li><a href="https://github.com/codemirror/codemirror">Code</a>
  </ul>
  <ul>
    <li><a href="../index.html">Language modes</a>
    <li><a class=active href="#">HTML mixed</a>
  </ul>
</div>

<article>
<h2>HTML mixed mode</h2>
<form><textarea id="code" name="code">
<html style="color: green">
  <!-- this is a comment -->
  <head>
    <title>Mixed HTML Example</title>
    <style>
      h1 {font-family: comic sans; color: #f0f;}
      div {background: yellow !important;}
      body {
        max-width: 50em;
        margin: 1em 2em 1em 5em;
      }
    </style>
  </head>
  <body>
    <h1>Mixed HTML Example</h1>
    <script>
      function jsFunc(arg1, arg2) {
        if (arg1 && arg2) document.body.innerHTML = "achoo";
      }
    </script>
  </body>
</html>
</textarea></form>
    <script>
      // Define an extended mixed-mode that understands vbscript and
      // leaves mustache/handlebars embedded templates in html mode
      var mixedMode = {
        name: "htmlmixed",
        scriptTypes: [{matches: /\/x-handlebars-template|\/x-mustache/i,
                       mode: null},
                      {matches: /(text|application)\/(x-)?vb(a|script)/i,
                       mode: "vbscript"}]
      };
      var editor = CodeMirror.fromTextArea(document.getElementById("code"), {
        mode: mixedMode,
        selectionPointer: true
      });
    </script>

    <p>The HTML mixed mode depends on the XML, JavaScript, and CSS modes.</p>

    <p>It takes an optional mode configuration
    option, <code>tags</code>, which can be used to add custom
    behavior for specific tags. When given, it should be an object
    mapping tag names (for example <code>script</code>) to arrays or
    three-element arrays. Those inner arrays indicate [attributeName,
    valueRegexp, <a href="../../doc/manual.html#option_mode">modeSpec</a>]
    specifications. For example, you could use <code>["type", /^foo$/,
    "foo"]</code> to map the attribute <code>type="foo"</code> to
    the <code>foo</code> mode. When the first two fields are null
    (<code>[null, null, "mode"]</code>), the given mode is used for
    any such tag that doesn't match any of the previously given
    attributes. For example:</p>

    <pre>var myModeSpec = {
  name: "htmlmixed",
  tags: {
    style: [["type", /^text\/(x-)?scss$/, "text/x-scss"],
            [null, null, "css"]],
    custom: [[null, null, "customMode"]]
  }
}</pre>

    <p><strong>MIME types defined:</strong> <code>text/html</code>
    (redefined, only takes effect if you load this parser after the
    XML parser).</p>

  </article>
