/*

THIS FILE IS O<PERSON><PERSON><PERSON>ITTEN EVERY TIME YOU UPDATE THE PLUGIN.
USE THE CUSOTM CSS OPTION IN THE SUBSCRIPTION SETTING PANEL FOR YOUR
CUSTOM CSS RULES.

*/

.tnp-subscription {
    font-size: 13px;
    display: block;
    margin: 15px auto;
    max-width: 500px;
    width: 100%;
}

/* Generic field wrapper */
.tnp-subscription div.tnp-field {
    margin-bottom: 10px;
    border: 0;
    padding: 0;
}

.tnp-subscription label {
    display: block;
    color: inherit;
    font-size: 14px;
    font-weight: 700;
    line-height: normal;
    padding: 5px;
    margin: 0;
}

.tnp-subscription input[type=text], 
.tnp-subscription input[type=email], 
.tnp-subscription input[type=submit], 
.tnp-subscription select {
    width: 100%;
    height: 50px;
    padding: 10px;
    display: block;
    border: 1px;
    border-color: #ddd;
    background-color: #f4f4f4;
    background-image: none;
    text-shadow: none;
    color: #444;
    font-size: 14px;
    line-height: 20px;
    margin: 0;
    line-height: normal;
    box-sizing: border-box;
}

.tnp-subscription input[type=checkbox], 
.tnp-widget input[type=radio] {
    max-width: 20px;
    display: inline-block;
}

/* Antireset - http://www.satollo.net/css-and-select-space-between-the-options-and-the-arrow */
.tnp-subscription select option {
    margin-right: 10px;
}

.tnp-subscription input.tnp-submit {
    background-color: #444;
    color: #fff;
    width: auto;
    height: auto;
    margin: 0;
}

@media all and (max-width: 480px) {
    .tnp-subscription input[type=submit] {
        width: 100%;
    }
}


/* Profile form */

.tnp-profile {
    font-size: 13px;
}

.tnp-profile form .tnp-field {
    margin-bottom: 10px;
    border: 0;
    padding: 0;
}

.tnp-profile form .tnp-field label {
    display: block;
    color: #333;
    font-size: 14px;
}

.tnp-profile form .tnp-field input[type=text], 
.tnp-profile form .tnp-field input[type=email], 
.tnp-profile form .tnp-field input[type=submit], 
.tnp-profile form .tnp-field textarea, 
.tnp-profile form .tnp-field select {
    padding: 10px;
    display: block;
    border: 1px;
    border-color: #ddd;
    background-color: #f4f4f4;
    background-image: none;
    text-shadow: none;
    color: #444;
    font-size: 14px;
    margin: 0;
    line-height: normal;
    box-sizing: border-box;
    border-radius: 0;
    height: auto;
    float: none;
}

.tnp-profile form input[type=checkbox], .tnp-profile input[type=radio] {
    max-width: 20px;
    display: inline-block;
}

.tnp-profile form .tnp-list-label {
    margin-left: 15px;
}

/* Antireset - http://www.satollo.net/css-and-select-space-between-the-options-and-the-arrow */
.tnp-profile form select option {
    margin-right: 10px;
}

.tnp-profile form .tnp-field input[type=submit] {
    background-color: #444;
    color: #fff;
    width: auto;
    height: auto;
    margin: 0;
}

@media all and (max-width: 480px) {
    .tnp-profile input[type=submit] {
        width: 100%;
        margin: 0;
    }
}



.tnp-widget {
    width: 100%;
    display: block;
    box-sizing: border-box;
}

.tnp-widget .tnp-field {
    margin-bottom: 10px;
    border: 0;
    padding: 0;
}

.tnp-widget label {
    display: block;
    color: inherit;
    font-size: 14px;
}

.tnp-widget input[type=text], .tnp-widget input[type=email], .tnp-widget input[type=submit], .tnp-widget select {
    width: 100%;
    padding: 10px;
    display: block;
    border: 1px solid #ddd ;
    border-color: #ddd;
    background-color: #f4f4f4;
    background-image: none;
    text-shadow: none;
    color: #444;
    font-size: 14px;
    line-height: normal;
    box-sizing: border-box;
    height: auto;
}

.tnp-widget input[type=checkbox], .tnp-widget input[type=radio] {
    width: auto;
    display: inline-block;
}

/* Antireset - http://www.satollo.net/css-and-select-space-between-the-options-and-the-arrow */
.tnp-widget select option {
    margin-right: 10px;
}

.tnp-widget input.tnp-submit {
    background-color: #444;
    background-image: none;
    text-shadow: none;
    color: #fff;
    margin: 0;
}

.tnp-field input[type="submit"] {
    position: inherit;
}

.tnp-field label {
    
}

/* Newsletter Widget Minimal */

.tnp-widget-minimal {
    width: 100%;
}

.tnp-widget-minimal form {
    margin: 0;
    padding: 0;
    border: 0;
}

.tnp-widget-minimal input.tnp-email {
    width: 100%;
    box-sizing: border-box;
    padding: 10px;
    display: inline-block;
    border: 1px solid #ddd;
    background-color: #f4f4f4;
    color: #444;
    font-size: 14px;
}

.tnp-widget-minimal input.tnp-submit {
    width: 100%;
    box-sizing: border-box;
    padding: 10px;
    display: inline-block;
    border: 1px;
    border-color: #ddd;
    background-color: #444;
    background-image: none;
    text-shadow: none;
    color: #fff;
    font-size: 14px;
    line-height: normal;
    border-radius: 0px;
    box-sizing: border-box;
    height: auto;
    margin: 0;
}

/* The minimal form */

.tnp-subscription-minimal {
    width: 100%;
    box-sizing: border-box;
}

.tnp-subscription-minimal form {
    margin: 0;
    padding: 0;
    border: 0;
}

.tnp-subscription-minimal input.tnp-email {
    width: 70%;
    max-width: 300px;
    box-sizing: border-box;
    padding: 10px;
    display: inline-block;
    border: 1px solid #ddd;
    background-color: #f4f4f4;
    color: #444;
    font-size: 14px;
    line-height: 20px;
    border-radius: 0px;
}

.tnp-subscription-minimal .tnp-privacy-field {
    margin-top: 10px;
}

.tnp-subscription-minimal input.tnp-submit {
    width: 29%;
    box-sizing: border-box;
    display: inline-block;
    padding: 10px;
    border: 1px;
    border-color: #ddd;
    background-color: #444;
    background-image: none;
    text-shadow: none;
    color: #fff;
    font-size: 14px;
    line-height: 20px;
    box-sizing: border-box;
    border-radius: 0px;
    margin: 0;
}

/* Comments Extension */
.tnp-comments {
    clear: both;
    margin-top: 15px;
    margin-bottom: 15px;
}

.tnp-comments label {
    display: block;
}

.tnp-comments input[type=checkbox] {
    display: inline-block;
    width: auto!important;
}


/* Locked Content Extension */
.tnp-lock {
    clear: both;
    display: block;
    box-sizing: border-box;
    box-shadow: none;
    margin: 20px;
    padding: 15px;
    background-color: #fff;
    border: 1px solid #ddd;
}
