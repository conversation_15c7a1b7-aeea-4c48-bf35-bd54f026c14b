<?php
if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

class chkyahoom<PERSON>chant extends be_module {
	public function process( $ip, &$stats = array(), &$options = array(), &$post = array() ) {
		$yahoo            = array(
			'***********/24',
			'**************/25',
			'***********/23',
			'************/22',
			'*************/24',
			'************/23',
			'************/25',
			'************/25',
			'************/19',
			'************/28',
			'*************/28',
			'*************/27',
			'**************/27',
			'************/24',
			'*************/22',
			'************/22',
			'************/22'
		);
		$this->searchname = 'Yahoo Merchant Services';

		return $this->searchList( $ip, $yahoo );

		return false;
	}
}

?>