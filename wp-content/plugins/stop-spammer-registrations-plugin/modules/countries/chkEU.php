<?php
// generated Saturday 11th of April 2015 04:12:12 PM
if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

class chkEU extends be_module {
	public $searchname = 'European Union';
	public $searchlist = array(
		array( '005104064000', '005104072000' ),
		array( '031192224000', '031192232000' ),
		array( '046022064000', '046022080000' ),
		array( '089030000000', '089030128000' ),
		array( '091224246000', '091224248000' ),
		array( '093170104000', '093170106000' ),
		array( '093184210000', '093184211000' ),
		array( '093184212000', '093184216000' ),
		array( '109206166000', '109206168000' ),
		array( '141101064000', '141101065000' ),
		array( '141101075000', '141101076000' ),
		array( '176103128000', '176103160000' ),
		array( '185040180000', '185040181000' ),
		array( '193105154000', '193105155000' ),
		array( '193106032000', '193106036000' ),
		array( '193200150000', '193200151000' ),
		array( '195242214000', '195242216000' ),
		array( '213238168000', '213238176000' )
	);
}

?>