<?php
// generated Saturday 11th of April 2015 04:12:08 PM
if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

class chkDO extends be_module {
	public $searchname = 'Dominican Republic';
	public $searchlist = array(
		array( '064032064000', '064032128000' ),
		array( '066098034000', '066098034008' ),
		array( '148000000000', '148001000000' ),
		array( '152166000000', '152167000000' ),
		array( '179043192000', '179043208000' ),
		array( '179052000000', '179054000000' ),
		array( '181036000000', '181036001000' ),
		array( '181036005000', '181036006000' ),
		array( '181037128000', '181038000000' ),
		array( '186001127000', '186001127128' ),
		array( '186006000000', '186008000000' ),
		array( '186120000000', '186121000000' ),
		array( '186149000000', '186151000000' ),
		array( '190008032000', '190008048000' ),
		array( '190094000000', '190094032000' ),
		array( '190122116128', '190122117000' ),
		array( '190166056208', '190166056216' ),
		array( '190166128000', '190168000000' ),
		array( '200026168000', '200026169000' ),
		array( '200026172176', '200026172184' ),
		array( '200088113216', '200088113224' ),
		array( '200088128000', '200089000000' ),
		array( '201229192000', '201230000000' )
	);
}

?>