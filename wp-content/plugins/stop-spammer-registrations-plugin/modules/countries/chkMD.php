<?php
// generated Saturday 11th of April 2015 04:12:43 PM
if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

class chkMD extends be_module {
	public $searchname = 'Moldova';
	public $searchlist = array(
		array( '031031000000', '031031032000' ),
		array( '037026128000', '037026144000' ),
		array( '037075016000', '037075032000' ),
		array( '037156247000', '037156248000' ),
		array( '037233000000', '037233064000' ),
		array( '046055000000', '046055128000' ),
		array( '046161063000', '046161064000' ),
		array( '062221064000', '062221128000' ),
		array( '077089192000', '077090000000' ),
		array( '077235096000', '077235128000' ),
		array( '079140160000', '079140176000' ),
		array( '080094241000', '080094242000' ),
		array( '080094246000', '080094247000' ),
		array( '080245080000', '080245096000' ),
		array( '083166232000', '083166235000' ),
		array( '083218202000', '083218203000' ),
		array( '084234048000', '084234064000' ),
		array( '084247044000', '084247046000' ),
		array( '086105056000', '086105064000' ),
		array( '086105208000', '086105212000' ),
		array( '086107160000', '086107168000' ),
		array( '087248172000', '087248173000' ),
		array( '087255064000', '087255096000' ),
		array( '089028000000', '089028128000' ),
		array( '089032226000', '089032227000' ),
		array( '089032229000', '089032231000' ),
		array( '089032239000', '089032240000' ),
		array( '089033000000', '089033004000' ),
		array( '089034112000', '089034120000' ),
		array( '089039096000', '089039104000' ),
		array( '089187032000', '089187064000' ),
		array( '091214200000', '091214204000' ),
		array( '091238094000', '091238096000' ),
		array( '092114128000', '092114192000' ),
		array( '093116000000', '093116064000' ),
		array( '093116128000', '093117000000' ),
		array( '093117048000', '093117064000' ),
		array( '093117168000', '093117172000' ),
		array( '094243112000', '094243128000' ),
		array( '095065000000', '095065128000' ),
		array( '095153064000', '095153128000' ),
		array( '109185000000', '109186000000' ),
		array( '176123000000', '176123032000' ),
		array( '176223152000', '176223156000' ),
		array( '178017162000', '178017163000' ),
		array( '178017166000', '178017167000' ),
		array( '178017170000', '178017171000' ),
		array( '178168000000', '178168128000' ),
		array( '178175131000', '178175132000' ),
		array( '178175136000', '178175144000' ),
		array( '185046060000', '185046061000' ),
		array( '188131080000', '188131112000' ),
		array( '188237000000', '188238000000' ),
		array( '188240096000', '188240128000' ),
		array( '188244016000', '188244032000' ),
		array( '193016111000', '193016112000' ),
		array( '193104041000', '193104042000' ),
		array( '193226064000', '193226065000' ),
		array( '194247164000', '194247166000' ),
		array( '212000208000', '212000224000' ),
		array( '212028064000', '212028072000' ),
		array( '212056192000', '212056224000' ),
		array( '217012112000', '217012128000' ),
		array( '217019208000', '217019209000' ),
		array( '217019212000', '217019213000' ),
		array( '217026160000', '217026161000' ),
		array( '217026164000', '217026165000' )
	);
}

?>