<?php
// generated Saturday 11th of April 2015 04:12:42 PM
if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

class chkLV extends be_module {
	public $searchname = 'Latvia';
	public $searchlist = array(
		array( '005044216000', '005044224000' ),
		array( '031184236000', '031184237000' ),
		array( '046109000000', '046110000000' ),
		array( '046183216000', '046183224000' ),
		array( '046243140000', '046243141000' ),
		array( '062205208000', '062205216000' ),
		array( '077038128000', '077039000000' ),
		array( '077093000000', '077093032000' ),
		array( '078084000000', '078085000000' ),
		array( '078154128000', '078154144000' ),
		array( '080090024000', '080090028000' ),
		array( '080090030000', '080090032000' ),
		array( '080232128000', '080233000000' ),
		array( '080233128000', '080234000000' ),
		array( '081094238000', '081094239000' ),
		array( '081163072000', '081163080000' ),
		array( '081198000000', '081199000000' ),
		array( '083099128000', '083100000000' ),
		array( '083223128000', '083223160000' ),
		array( '083241040000', '083241048000' ),
		array( '084237128000', '084238000000' ),
		array( '084245192000', '084246000000' ),
		array( '085009208000', '085009224000' ),
		array( '085015224000', '085016000000' ),
		array( '085031096000', '085031104000' ),
		array( '085158072000', '085158080000' ),
		array( '085254032000', '085254040000' ),
		array( '087110000000', '087111000000' ),
		array( '087226000000', '087226128000' ),
		array( '087246128000', '087246144000' ),
		array( '088135128000', '088135160000' ),
		array( '089018192000', '089018224000' ),
		array( '089111023000', '089111024000' ),
		array( '089201000000', '089201128000' ),
		array( '091105000000', '091105128000' ),
		array( '091135016000', '091135032000' ),
		array( '091188032000', '091188064000' ),
		array( '091199086000', '091199087000' ),
		array( '091220043000', '091220044000' ),
		array( '091224013000', '091224014000' ),
		array( '092063080000', '092063096000' ),
		array( '093177192000', '093178000000' ),
		array( '094103050000', '094103051000' ),
		array( '095131112000', '095131116000' ),
		array( '109110000000', '109110032000' ),
		array( '109229192000', '109229224000' ),
		array( '136169000000', '136169064000' ),
		array( '136169112000', '136169128000' ),
		array( '159148000000', '159149000000' ),
		array( '176106048000', '176106064000' ),
		array( '178016016000', '178016032000' ),
		array( '185061148000', '185061150000' ),
		array( '185069024000', '185069028000' ),
		array( '188092020000', '188092021000' ),
		array( '188092072000', '188092080000' ),
		array( '188112128000', '188112192000' ),
		array( '193110164000', '193110166000' ),
		array( '193169013000', '193169014000' ),
		array( '193169195000', '193169196000' ),
		array( '195003144000', '195003148000' ),
		array( '195013128000', '195014000000' ),
		array( '195043082000', '195043084000' ),
		array( '195122000000', '195122032000' ),
		array( '195130205000', '195130206000' ),
		array( '212093097000', '212093098000' ),
		array( '212093114000', '212093118000' ),
		array( '212093122000', '212093124000' ),
		array( '212142064000', '212142128000' ),
		array( '213175088000', '213175096000' ),
		array( '213180096000', '213180128000' ),
		array( '217024064000', '217024080000' ),
		array( '217198224000', '217198240000' )
	);
}

?>