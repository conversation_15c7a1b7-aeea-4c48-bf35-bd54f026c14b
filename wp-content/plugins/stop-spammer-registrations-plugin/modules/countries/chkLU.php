<?php
// generated Saturday 11th of April 2015 04:12:41 PM
if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

class chkLU extends be_module {
	public $searchname = 'Luxembourg';
	public $searchlist = array(
		array( '005149112000', '005149120000' ),
		array( '005157040000', '005157043000' ),
		array( '031006052000', '031006053000' ),
		array( '046226104000', '046226112000' ),
		array( '078141128000', '078141192000' ),
		array( '080090032000', '080090064000' ),
		array( '083099000000', '083099128000' ),
		array( '085093192000', '085093224000' ),
		array( '087240192000', '087241000000' ),
		array( '087254096000', '087254128000' ),
		array( '088207128000', '088208000000' ),
		array( '091214044000', '091214048000' ),
		array( '094242192000', '094243000000' ),
		array( '094252024000', '094252032000' ),
		array( '095141029000', '095141030000' ),
		array( '128127109000', '128127110000' ),
		array( '176065077032', '176065077048' ),
		array( '185005045000', '185005046000' ),
		array( '185049244000', '185049248000' ),
		array( '195218028000', '195218032000' ),
		array( '212117160000', '212117192000' ),
		array( '212233032000', '212233064000' ),
		array( '213135224000', '213136000000' )
	);
}

?>