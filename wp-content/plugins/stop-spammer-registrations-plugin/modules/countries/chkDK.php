<?php
// generated Saturday 11th of April 2015 04:12:07 PM
if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

class chkDK extends be_module {
	public $searchname = 'Denmark';
	public $searchlist = array(
		array( '002104000000', '002112000000' ),
		array( '005103000000', '005104000000' ),
		array( '031006031000', '031006032000' ),
		array( '037049128000', '037049144000' ),
		array( '037060160000', '037060168000' ),
		array( '037075160000', '037075192000' ),
		array( '062044128000', '062044192000' ),
		array( '062107000000', '062108000000' ),
		array( '062198000000', '062200000000' ),
		array( '062242000000', '062244000000' ),
		array( '077066000000', '077066128000' ),
		array( '077068128000', '077069000000' ),
		array( '077212000000', '077216000000' ),
		array( '078111160000', '078111176000' ),
		array( '078157112000', '078157120000' ),
		array( '079141165000', '079141166000' ),
		array( '079142069000', '079142070000' ),
		array( '080062000000', '080064000000' ),
		array( '080071128000', '080071144000' ),
		array( '080080000000', '080080032000' ),
		array( '080160000000', '080168000000' ),
		array( '080196000000', '080200000000' ),
		array( '081007128000', '081007192000' ),
		array( '081019224000', '081020000000' ),
		array( '082103128000', '082103192000' ),
		array( '082211192000', '082211224000' ),
		array( '083088000000', '083096000000' ),
		array( '085027128000', '085028000000' ),
		array( '085218128000', '085219000000' ),
		array( '086052000000', '086053000000' ),
		array( '086058128000', '086059000000' ),
		array( '087048000000', '087064000000' ),
		array( '087072000000', '087073000000' ),
		array( '087104000000', '087105000000' ),
		array( '089184152000', '089184156000' ),
		array( '090184000000', '090186000000' ),
		array( '091100000000', '091102000000' ),
		array( '091238206000', '091238207000' ),
		array( '092043176000', '092043184000' ),
		array( '092246000000', '092246032000' ),
		array( '093160000000', '093168000000' ),
		array( '093184192000', '093184208000' ),
		array( '094018000000', '094019000000' ),
		array( '094126176000', '094126184000' ),
		array( '094144000000', '094152000000' ),
		array( '094191128000', '094192000000' ),
		array( '095154016000', '095154032000' ),
		array( '109056000000', '109060000000' ),
		array( '109202128000', '109202160000' ),
		array( '130225000000', '130227000000' ),
		array( '152115064000', '152115128000' ),
		array( '159253131128', '159253132000' ),
		array( '159253143176', '159253143184' ),
		array( '159253149184', '159253149192' ),
		array( '159253150144', '159253150152' ),
		array( '159253150192', '159253150200' ),
		array( '178157192000', '178158000000' ),
		array( '185020240000', '185020244000' ),
		array( '188114128000', '188114192000' ),
		array( '188176000000', '188184000000' ),
		array( '193088000000', '193090000000' ),
		array( '193163220000', '193163221000' ),
		array( '193202110000', '193202111000' ),
		array( '194150112000', '194150116000' ),
		array( '194239000000', '194240000000' ),
		array( '194255000000', '195000000000' ),
		array( '195128174000', '195128176000' ),
		array( '212010000000', '212011000000' ),
		array( '212097128000', '212097160000' ),
		array( '212130000000', '212131000000' ),
		array( '212242000000', '212243000000' )
	);
}

?>