<?php
// generated Saturday 11th of April 2015 04:13:19 PM
if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

class chkTH extends be_module {
	public $searchname = 'Thailand';
	public $searchlist = array(
		array( '001000128000', '001001000000' ),
		array( '001001128000', '001002000000' ),
		array( '001002128000', '001003000000' ),
		array( '001004128000', '001005000000' ),
		array( '001010192000', '001011000000' ),
		array( '001046000000', '001048000000' ),
		array( '001179128000', '001179224000' ),
		array( '014128010000', '014128011000' ),
		array( '014207000000', '014208000000' ),
		array( '027055000000', '027056000000' ),
		array( '027130000000', '027131000000' ),
		array( '027131128000', '027131192000' ),
		array( '027145000000', '027146000000' ),
		array( '027254016000', '027254017000' ),
		array( '027254044000', '027254045000' ),
		array( '027254060000', '027254061000' ),
		array( '027254062000', '027254063000' ),
		array( '027254082000', '027254083000' ),
		array( '027254086000', '027254087000' ),
		array( '027254141000', '027254142000' ),
		array( '027254144000', '027254145000' ),
		array( '043254132000', '043254136000' ),
		array( '049000064000', '049000065000' ),
		array( '049000072000', '049000073000' ),
		array( '049000088000', '049000089000' ),
		array( '049000105000', '049000106000' ),
		array( '049048000000', '049050000000' ),
		array( '049228224000', '049228240000' ),
		array( '049230016000', '049230032000' ),
		array( '049230080000', '049230224000' ),
		array( '049231000000', '049232000000' ),
		array( '049237000000', '049237064000' ),
		array( '058008000000', '058011128000' ),
		array( '058011192000', '058012000000' ),
		array( '058064037000', '058064038000' ),
		array( '058097000000', '058097128000' ),
		array( '058137028016', '058137028032' ),
		array( '058137061208', '058137061224' ),
		array( '058137145224', '058137146000' ),
		array( '058181128000', '058182000000' ),
		array( '061007128000', '061008000000' ),
		array( '061019000000', '061019224000' ),
		array( '061019240000', '061020000000' ),
		array( '061090000000', '061090128000' ),
		array( '101108000000', '101108160000' ),
		array( '101108192000', '101109243000' ),
		array( '101109248000', '101109249000' ),
		array( '103004218000', '103004219000' ),
		array( '103007056000', '103007060000' ),
		array( '103010228000', '103010229000' ),
		array( '103010231000', '103010232000' ),
		array( '103013029000', '103013030000' ),
		array( '103022180128', '103022182000' ),
		array( '103228004000', '103228008000' ),
		array( '103233194000', '103233195000' ),
		array( '103243168000', '103243172000' ),
		array( '103246016000', '103246020000' ),
		array( '103253072000', '103253076000' ),
		array( '106000128000', '106001000000' ),
		array( '110034168000', '110034180000' ),
		array( '110034192000', '110034208000' ),
		array( '110034224000', '110034240000' ),
		array( '110049208000', '110049212000' ),
		array( '110077128000', '110078000000' ),
		array( '110078128000', '110078192000' ),
		array( '110164000000', '110164032000' ),
		array( '110164048000', '110164080000' ),
		array( '110164112000', '110164128000' ),
		array( '110164192000', '110165000000' ),
		array( '110168000000', '110170000000' ),
		array( '110170128000', '110171032000' ),
		array( '110171112000', '110171120000' ),
		array( '110171160000', '110171176000' ),
		array( '110171224000', '110171240000' ),
		array( '111092180000', '111092180128' ),
		array( '112142000000', '112144000000' ),
		array( '113021246000', '113021247000' ),
		array( '113053000000', '113054000000' ),
		array( '115031128000', '115031192000' ),
		array( '116058224000', '116059000000' ),
		array( '118172000000', '118174160000' ),
		array( '118174192000', '118174224000' ),
		array( '118175002000', '118175004000' ),
		array( '118175005000', '118175006000' ),
		array( '118175009000', '118175010000' ),
		array( '118175012000', '118175013000' ),
		array( '118175014000', '118175015000' ),
		array( '118175023000', '118175024000' ),
		array( '118175029000', '118175030000' ),
		array( '118175064000', '118175096000' ),
		array( '118175128000', '118176000000' ),
		array( '119031120000', '119031128000' ),
		array( '119042064000', '119042128000' ),
		array( '119046064000', '119046160000' ),
		array( '119046176000', '119047000000' ),
		array( '119059096000', '119059128000' ),
		array( '119063083032', '119063083064' ),
		array( '119076000000', '119076128000' ),
		array( '119160208000', '119160224000' ),
		array( '122154000000', '122154004000' ),
		array( '122154032000', '122154160000' ),
		array( '122154224000', '122155016000' ),
		array( '122155032000', '122155044000' ),
		array( '122155160000', '122155224000' ),
		array( '123242160000', '123242192000' ),
		array( '124109001000', '124109003000' ),
		array( '125024000000', '125028000000' ),
		array( '134196000000', '134197000000' ),
		array( '161246000000', '161247000000' ),
		array( '164115000000', '164116000000' ),
		array( '171004000000', '171008000000' ),
		array( '171096000000', '171099000000' ),
		array( '171099128000', '171102000000' ),
		array( '175176220000', '175176224000' ),
		array( '180180000000', '180180192000' ),
		array( '180180224000', '180180240000' ),
		array( '180183000000', '180184000000' ),
		array( '180222154000', '180222155000' ),
		array( '182052000000', '182052129000' ),
		array( '182052138000', '182052139000' ),
		array( '182052143000', '182052144000' ),
		array( '182052160000', '182053000000' ),
		array( '182053032000', '182053160000' ),
		array( '182053224000', '182054000000' ),
		array( '182093128000', '182094000000' ),
		array( '183088000000', '183090000000' ),
		array( '202014117000', '202014118000' ),
		array( '202028000000', '202030000000' ),
		array( '202041160000', '202041192000' ),
		array( '202044032000', '202044048000' ),
		array( '202044064000', '202044066000' ),
		array( '202044192000', '202045000000' ),
		array( '202060199000', '202060200000' ),
		array( '202091016000', '202091024000' ),
		array( '202129028000', '202129032000' ),
		array( '202129052000', '202129056000' ),
		array( '202129204000', '202129208000' ),
		array( '202143128000', '202143192000' ),
		array( '202151004000', '202151008000' ),
		array( '202162076000', '202162080000' ),
		array( '202173217128', '202173217136' ),
		array( '202173222000', '202173222128' ),
		array( '202183220000', '202183221000' ),
		array( '203113096000', '203113128000' ),
		array( '203114096000', '203114128000' ),
		array( '203144128000', '203145000000' ),
		array( '203146037208', '203146037240' ),
		array( '203146082000', '203146083000' ),
		array( '203146175008', '203146175016' ),
		array( '203146235000', '203146236000' ),
		array( '203147011184', '203147011192' ),
		array( '203147034000', '203147035000' ),
		array( '203149000000', '203149064000' ),
		array( '203150000000', '203150128000' ),
		array( '203151000000', '203151017112' ),
		array( '203151020000', '203151022000' ),
		array( '203152026000', '203152026128' ),
		array( '203155170224', '203155170240' ),
		array( '203156123000', '203156128000' ),
		array( '203156150000', '203156152000' ),
		array( '203158096000', '203160000000' ),
		array( '203170248016', '203170248032' ),
		array( '203172128000', '203173000000' ),
		array( '203185096000', '203185128000' ),
		array( '203209000000', '203209128000' ),
		array( '210213032000', '210213064000' ),
		array( '210246092000', '210246093000' ),
		array( '223025192000', '223025224000' ),
		array( '223027192000', '223027193040' )
	);
}

?>