<?php
// generated Saturday 11th of April 2015 04:12:41 PM
if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

class chkLT extends be_module {
	public $searchname = 'Lithuania';
	public $searchlist = array(
		array( '005020032000', '005020064000' ),
		array( '005020104000', '005020112000' ),
		array( '005020128000', '005020192000' ),
		array( '005199164000', '005199172000' ),
		array( '031193192000', '031193200000' ),
		array( '046148030000', '046148032000' ),
		array( '046166160000', '046166168000' ),
		array( '077079000000', '077079016000' ),
		array( '077079032000', '077079048000' ),
		array( '077087008000', '077087016000' ),
		array( '077090072000', '077090076000' ),
		array( '077090092000', '077090094000' ),
		array( '077090096000', '077090098000' ),
		array( '077221092000', '077221096000' ),
		array( '078056000000', '078056016000' ),
		array( '078056032000', '078056048000' ),
		array( '078056064000', '078056080000' ),
		array( '078056096000', '078056112000' ),
		array( '078056208000', '078056224000' ),
		array( '078057208000', '078057224000' ),
		array( '078058048000', '078058064000' ),
		array( '078058128000', '078058160000' ),
		array( '078060048000', '078060064000' ),
		array( '078060096000', '078060112000' ),
		array( '078060128000', '078060160000' ),
		array( '078061000000', '078061016000' ),
		array( '078062080000', '078062096000' ),
		array( '078063080000', '078063096000' ),
		array( '078063128000', '078063144000' ),
		array( '078063208000', '078063224000' ),
		array( '078157064000', '078157096000' ),
		array( '078158000000', '078158016000' ),
		array( '081007096000', '081007104000' ),
		array( '082135200000', '082135208000' ),
		array( '082135240000', '082135248000' ),
		array( '082198000000', '082198032000' ),
		array( '084015000000', '084016000000' ),
		array( '084032140000', '084032144000' ),
		array( '084046224000', '084047000000' ),
		array( '084240000000', '084240064000' ),
		array( '085206000000', '085206008000' ),
		array( '085206024000', '085206032000' ),
		array( '085206128000', '085206136000' ),
		array( '086038000000', '086039000000' ),
		array( '086100000000', '086100128000' ),
		array( '086100240000', '086100244000' ),
		array( '088119000000', '088119064000' ),
		array( '088119128000', '088120000000' ),
		array( '088216008000', '088216016000' ),
		array( '088216112000', '088216113000' ),
		array( '088222000000', '088222128000' ),
		array( '088223064000', '088223072000' ),
		array( '089116128000', '089117000000' ),
		array( '089117128000', '089118000000' ),
		array( '089249080000', '089249096000' ),
		array( '091211244000', '091211248000' ),
		array( '092061032000', '092061048000' ),
		array( '092062128000', '092062144000' ),
		array( '094244064000', '094244128000' ),
		array( '176031000000', '176032000000' ),
		array( '178236204000', '178236208000' ),
		array( '185005052000', '185005056000' ),
		array( '185008107000', '185008108000' ),
		array( '185025048000', '185025052000' ),
		array( '185065200000', '185065202000' ),
		array( '185069052000', '185069056000' ),
		array( '185080128000', '185080132000' ),
		array( '188069192000', '188069224000' ),
		array( '193041197000', '193041198000' ),
		array( '193106031000', '193106032000' ),
		array( '193189086000', '193189088000' ),
		array( '193219144000', '193219152000' ),
		array( '193219160000', '193219192000' ),
		array( '194135080000', '194135096000' ),
		array( '195014160000', '195014192000' ),
		array( '195182064000', '195182096000' ),
		array( '212052040000', '212052048000' ),
		array( '212052062000', '212052064000' ),
		array( '212117000000', '212117032000' ),
		array( '213159032000', '213159064000' ),
		array( '213164096000', '213164128000' ),
		array( '213197128000', '213197192000' ),
		array( '213252244000', '213252248000' ),
		array( '217147041000', '217147042000' )
	);
}

?>