<?php
// generated Saturday 11th of April 2015 04:11:50 PM
if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

class chkBE extends be_module {
	public $searchname = 'Belgium';
	public $searchlist = array(
		array( '005134000000', '005134002000' ),
		array( '031005000000', '031006000000' ),
		array( '062197112000', '062197128000' ),
		array( '062205064000', '062205128000' ),
		array( '077109096000', '077109104000' ),
		array( '078020000000', '078024000000' ),
		array( '079142068000', '079142069000' ),
		array( '080200000000', '080202000000' ),
		array( '080236128000', '080237000000' ),
		array( '081011128000', '081012000000' ),
		array( '081082000000', '081084000000' ),
		array( '081095120000', '081095128000' ),
		array( '081164000000', '081166000000' ),
		array( '081171052000', '081171056000' ),
		array( '081240000000', '081248000000' ),
		array( '082143064000', '082143128000' ),
		array( '083101024000', '083101048000' ),
		array( '083101064000', '083101096000' ),
		array( '083134000000', '083135000000' ),
		array( '084192000000', '084200000000' ),
		array( '085010064000', '085010080000' ),
		array( '085027000000', '085027004000' ),
		array( '085027036000', '085027040000' ),
		array( '085027048000', '085027052000' ),
		array( '085088032000', '085088064000' ),
		array( '085201012000', '085201016000' ),
		array( '085201156000', '085201160000' ),
		array( '086039152000', '086039160000' ),
		array( '087064000000', '087068000000' ),
		array( '087238160000', '087238168000' ),
		array( '088147000000', '088147128000' ),
		array( '089106240000', '089106248000' ),
		array( '089163128000', '089164000000' ),
		array( '091176000000', '091184000000' ),
		array( '094104000000', '094112000000' ),
		array( '094224000000', '094228000000' ),
		array( '109088128000', '109088144000' ),
		array( '109088160000', '109088176000' ),
		array( '109089208000', '109089224000' ),
		array( '109128000000', '109136000000' ),
		array( '109140000000', '109144000000' ),
		array( '109236128000', '109236144000' ),
		array( '130104000000', '130105000000' ),
		array( '134054000000', '134055000000' ),
		array( '134058000000', '134059000000' ),
		array( '134184000000', '134185000000' ),
		array( '141134128000', '141135000000' ),
		array( '145129000000', '145129128000' ),
		array( '164015000000', '164016000000' ),
		array( '178050000000', '178052000000' ),
		array( '178145000000', '178146000000' ),
		array( '185029165000', '185029166000' ),
		array( '185035164000', '185035165000' ),
		array( '188044064000', '188044096000' ),
		array( '192071249000', '192071250000' ),
		array( '192135168000', '192135169000' ),
		array( '193053004000', '193053005000' ),
		array( '193075128000', '193076000000' ),
		array( '193190000000', '193192000000' ),
		array( '194050177000', '194050178000' ),
		array( '195177083000', '195177084000' ),
		array( '212068252000', '212069000000' ),
		array( '212079064000', '212079096000' ),
		array( '212088230000', '212088231000' ),
		array( '212088240000', '212089000000' ),
		array( '212123016000', '212123032000' ),
		array( '213049000000', '213050000000' ),
		array( '213118000000', '213120000000' ),
		array( '213213224000', '213213228000' ),
		array( '213214032000', '213214064000' ),
		array( '213251064000', '213251128000' ),
		array( '217015224000', '217015240000' )
	);
}

?>