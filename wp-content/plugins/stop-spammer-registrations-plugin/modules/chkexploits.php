<?php
if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

class chkexploits {
	public function process( $ip, &$stats = array(), &$options = array(), &$post = array() ) {
// search the request for eval and SQL statements
		$rpost = $_REQUEST;
		if ( empty( $rpost ) || ! is_array( $rpost ) ) {
			return false;
		}
		foreach ( $rpost as $req ) {
			if ( is_array( $req ) ) {
				$req = print_r( $req, true );
			}
			$req = urldecode( $req );
			if ( stripos( $req, 'eval' . '(base64' . '_decode(' ) !== false ) { // dotting the search to not kick off updates, etc.
				if ( strlen( $req ) > 24 ) {
					$req = substr( $req, 24 );
				}
				$req = htmlentities( $req );

				return "Eval Attack $req";
			}
			if ( stripos( $req, 'document.write(string.fromcharcode' ) !== false ) {
				if ( strlen( $req ) > 24 ) {
					$req = substr( $req, 24 );
				}
				$req = htmlentities( $req );

				return "Offset String Attack $req";
			}
// 'document.write(Stringfromcharcode'
// union all select - this is a common SQL injection string
			if ( stripos( $req, 'union all select' ) !== false ) {
				if ( strlen( $req ) > 24 ) {
					$req = substr( $req, 24 );
				}
				$req = htmlentities( $req );

				return "SQL Inject Attack $req";
			}
		}

		return false;
	}
}

?>